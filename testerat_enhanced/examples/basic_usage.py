#!/usr/bin/env python3
"""
Enhanced Testerat - Basic Usage Examples

This file demonstrates how to use Enhanced Testerat programmatically
for comprehensive web application testing.
"""

import sys
import os
import logging
from typing import Dict, Any

# Add the parent directory to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from testerat_enhanced import EnhancedTesterat, UniversalTestConfig
from testerat_enhanced.config.test_config import FrameworkType, AuthConfig, WorkflowConfig, APIConfig

def basic_testing_example():
    """Basic testing example with default configuration"""
    print("🎯 Basic Testing Example")
    print("=" * 50)
    
    # Create default configuration
    config = UniversalTestConfig()
    config.headless = True  # Run in headless mode for CI/CD
    
    # Initialize testerat
    testerat = EnhancedTesterat(config)
    
    # Run comprehensive testing
    results = testerat.run_comprehensive_test(
        url="https://example.com",
        description="Basic Testing Example"
    )
    
    # Print results summary
    print(f"✅ Tests completed!")
    print(f"   Total tests: {results['summary']['total_tests']}")
    print(f"   Passed: {results['summary']['passed']}")
    print(f"   Failed: {results['summary']['failed']}")
    print(f"   Success rate: {results['summary']['success_rate']:.1f}%")
    
    # Check for critical issues
    if results['critical_issues']:
        print(f"\n🚨 {len(results['critical_issues'])} critical issues found:")
        for issue in results['critical_issues']:
            print(f"   - {issue['test_name']}: {issue['details']}")
    else:
        print("\n✅ No critical issues found!")
    
    return results

def framework_specific_testing():
    """Example of testing with framework-specific optimizations"""
    print("\n🔧 Framework-Specific Testing Example")
    print("=" * 50)
    
    # Create configuration for React application
    config = UniversalTestConfig()
    config.framework = FrameworkType.REACT
    config.headless = True
    
    # Customize React-specific settings
    config.workflow_config.step_timeout = 8000  # React state updates
    config.auth_config.selectors.update({
        "loading_indicator": ".loading, [data-testid='loading']",
        "auth_indicator": "[data-testid='user-menu'], .user-avatar"
    })
    
    testerat = EnhancedTesterat(config)
    
    # Test a React application
    results = testerat.run_comprehensive_test(
        url="https://react-app.example.com",
        description="React App Testing"
    )
    
    print(f"Framework detected: {results['session']['framework']}")
    print(f"Success rate: {results['summary']['success_rate']:.1f}%")
    
    return results

def custom_authentication_testing():
    """Example of testing with custom authentication configuration"""
    print("\n🔐 Custom Authentication Testing Example")
    print("=" * 50)
    
    # Create configuration with custom auth settings
    config = UniversalTestConfig()
    config.headless = True
    
    # Customize authentication configuration
    config.auth_config = AuthConfig()
    config.auth_config.login_url = "/signin"
    config.auth_config.logout_url = "/signout"
    config.auth_config.test_users = {
        "standard": {
            "email": "<EMAIL>",
            "password": "testpassword123"
        },
        "admin": {
            "email": "<EMAIL>", 
            "password": "adminpassword123"
        }
    }
    
    # Custom selectors for this application
    config.auth_config.selectors.update({
        "email_input": "#email, [name='email']",
        "password_input": "#password, [name='password']",
        "login_button": "button[type='submit'], .login-btn",
        "logout_button": ".logout, [data-testid='logout']",
        "auth_indicator": ".user-profile, .logged-in-user"
    })
    
    testerat = EnhancedTesterat(config)
    
    # Run authentication-focused testing
    results = testerat.run_comprehensive_test(
        url="https://app-with-auth.example.com",
        description="Custom Auth Testing"
    )
    
    # Check authentication-specific results
    auth_results = [r for r in results['all_results'] if 'auth' in r['test_name'].lower()]
    print(f"Authentication tests: {len(auth_results)}")
    
    for result in auth_results:
        status_icon = "✅" if result['status'] == 'PASSED' else "❌"
        print(f"   {status_icon} {result['test_name']}: {result['status']}")
    
    return results

def api_testing_example():
    """Example of API-focused testing"""
    print("\n🌐 API Testing Example")
    print("=" * 50)
    
    config = UniversalTestConfig()
    config.headless = True
    
    # Focus on API testing
    config.test_authentication = False  # Skip auth for API-only testing
    config.test_workflows = False       # Skip workflows
    config.test_api_interactions = True # Focus on API
    
    # Customize API configuration
    config.api_config.common_endpoints = [
        "/api/health",
        "/api/status", 
        "/api/version",
        "/api/users",
        "/api/data"
    ]
    
    config.api_config.csrf_header_variations = [
        "X-CSRF-Token",
        "x-csrf-token", 
        "csrf-token",
        "X-CSRFToken"
    ]
    
    testerat = EnhancedTesterat(config)
    
    results = testerat.run_comprehensive_test(
        url="https://api.example.com",
        description="API Testing"
    )
    
    # Check API-specific results
    api_results = [r for r in results['all_results'] if 'api' in r['test_name'].lower()]
    print(f"API tests: {len(api_results)}")
    
    for result in api_results:
        status_icon = "✅" if result['status'] == 'PASSED' else "❌"
        print(f"   {status_icon} {result['test_name']}: {result['status']}")
    
    return results

def comprehensive_testing_example():
    """Example of comprehensive testing with all features enabled"""
    print("\n🎯 Comprehensive Testing Example")
    print("=" * 50)
    
    config = UniversalTestConfig()
    config.headless = True
    
    # Enable all testing features
    config.test_authentication = True
    config.test_workflows = True
    config.test_api_interactions = True
    
    # Comprehensive configuration
    config.auth_config.test_users = {
        "user1": {"email": "<EMAIL>", "password": "password123"},
        "user2": {"email": "<EMAIL>", "password": "password123"}
    }
    
    config.workflow_config.max_steps = 10
    config.workflow_config.step_timeout = 10000
    
    config.api_config.test_csrf_protection = True
    config.api_config.test_rate_limiting = True
    
    testerat = EnhancedTesterat(config)
    
    results = testerat.run_comprehensive_test(
        url="https://comprehensive-app.example.com",
        description="Full Comprehensive Testing"
    )
    
    # Detailed results analysis
    print(f"📊 Comprehensive Results:")
    print(f"   Total tests: {results['summary']['total_tests']}")
    print(f"   Success rate: {results['summary']['success_rate']:.1f}%")
    print(f"   Execution time: {results['session']['execution_time']:.1f}s")
    print(f"   Framework: {results['session']['framework']}")
    
    # Group results by category
    categories = {}
    for result in results['all_results']:
        category = result['test_name'].split('_')[0]
        if category not in categories:
            categories[category] = []
        categories[category].append(result)
    
    print(f"\n📋 Results by category:")
    for category, tests in categories.items():
        passed = len([t for t in tests if t['status'] == 'PASSED'])
        total = len(tests)
        print(f"   {category.title()}: {passed}/{total} passed")
    
    return results

def error_handling_example():
    """Example demonstrating error handling with unreachable sites"""
    print("\n⚠️  Error Handling Example")
    print("=" * 50)
    
    config = UniversalTestConfig()
    config.headless = True
    config.timeout = 5000  # Short timeout for demonstration
    
    testerat = EnhancedTesterat(config)
    
    # Test with unreachable site
    results = testerat.run_comprehensive_test(
        url="http://nonexistent-site-12345.com",
        description="Error Handling Test"
    )
    
    print(f"Error handling test completed:")
    print(f"   Status: {'✅ Handled gracefully' if results else '❌ Failed'}")
    
    if results:
        print(f"   Failed tests: {results['summary']['failed']}")
        print(f"   Error details available in results")
    
    return results

def main():
    """Run all examples"""
    print("🎯 Enhanced Testerat - Usage Examples")
    print("=" * 60)
    
    examples = [
        ("Basic Testing", basic_testing_example),
        ("Framework-Specific Testing", framework_specific_testing),
        ("Custom Authentication", custom_authentication_testing),
        ("API Testing", api_testing_example),
        ("Comprehensive Testing", comprehensive_testing_example),
        ("Error Handling", error_handling_example)
    ]
    
    results = {}
    
    for name, example_func in examples:
        try:
            print(f"\n🚀 Running {name}...")
            result = example_func()
            results[name] = result
            print(f"✅ {name} completed successfully")
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results[name] = None
    
    print(f"\n📊 Examples Summary:")
    print(f"   Total examples: {len(examples)}")
    print(f"   Successful: {len([r for r in results.values() if r is not None])}")
    print(f"   Failed: {len([r for r in results.values() if r is None])}")
    
    return results

if __name__ == "__main__":
    main()
