#!/usr/bin/env python3
"""
Enhanced Testerat Command Line Interface

Universal web testing framework that works with any web application.
Provides comprehensive testing with actionable insights and fix recommendations.

Usage:
    python -m testerat_enhanced.cli https://example.com
    python -m testerat_enhanced.cli http://localhost:3000 "FAAFO Career Platform Testing"
"""

import sys
import argparse
import json
from pathlib import Path

from .core.testerat_enhanced import EnhancedTesterat
from .config.test_config import UniversalTestConfig, FrameworkType


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="🎯 Enhanced Testerat - Universal Web Testing Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test any web application
  python -m testerat_enhanced.cli https://example.com
  
  # Test local development server
  python -m testerat_enhanced.cli http://localhost:3000
  
  # Test with custom description
  python -m testerat_enhanced.cli https://myapp.com "My App Testing"
  
  # Test with specific framework optimization
  python -m testerat_enhanced.cli https://myapp.com --framework react
  
  # Test with custom configuration
  python -m testerat_enhanced.cli https://myapp.com --config config.json
  
  # Headless mode (default)
  python -m testerat_enhanced.cli https://myapp.com --headless
  
  # Visible browser mode
  python -m testerat_enhanced.cli https://myapp.com --no-headless

Universal Support:
  ✅ React, Vue, Angular, Next.js, Nuxt.js, Svelte
  ✅ Any authentication system (NextAuth, Auth0, custom)
  ✅ Any API backend (Node.js, Python, PHP, etc.)
  ✅ Any deployment environment (local, staging, production)

Critical Issue Detection:
  🚨 Authentication state bugs (useSession loading issues)
  🚨 Workflow navigation failures (duplicate case statements)
  🚨 CSRF token header mismatches (X-CSRF-Token vs x-csrf-token)
  🚨 API endpoint failures and network errors
  🚨 File upload functionality and validation issues
  🚨 Modal/popup accessibility and interaction problems
  🚨 Security vulnerabilities and accessibility issues
        """
    )
    
    # Required arguments
    parser.add_argument(
        'url',
        help='URL of the web application to test'
    )
    
    parser.add_argument(
        'description',
        nargs='?',
        default='Comprehensive Testing',
        help='Description of the test session (default: "Comprehensive Testing")'
    )
    
    # Optional arguments
    parser.add_argument(
        '--framework',
        choices=['react', 'vue', 'angular', 'nextjs', 'nuxtjs', 'svelte', 'auto'],
        default='auto',
        help='Target framework for optimization (default: auto-detect)'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Path to custom configuration JSON file'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        default=True,
        help='Run in headless mode (default)'
    )
    
    parser.add_argument(
        '--no-headless',
        action='store_true',
        help='Run with visible browser'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='testerat_reports',
        help='Output directory for reports (default: testerat_reports)'
    )
    
    parser.add_argument(
        '--skip-auth',
        action='store_true',
        help='Skip authentication testing'
    )
    
    parser.add_argument(
        '--skip-workflows',
        action='store_true',
        help='Skip workflow testing'
    )
    
    parser.add_argument(
        '--skip-api',
        action='store_true',
        help='Skip API testing'
    )
    
    parser.add_argument(
        '--skip-file-uploads',
        action='store_true',
        help='Skip file upload testing'
    )
    
    parser.add_argument(
        '--skip-modals',
        action='store_true',
        help='Skip modal and popup testing'
    )
    
    # Enhanced CLI options for comprehensive control
    parser.add_argument(
        '--skip-security',
        action='store_true',
        help='Skip security testing'
    )
    
    parser.add_argument(
        '--skip-accessibility',
        action='store_true',
        help='Skip accessibility testing'
    )
    
    parser.add_argument(
        '--skip-performance',
        action='store_true',
        help='Skip performance testing'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=30000,
        help='Custom timeout setting in milliseconds (default: 30000)'
    )
    
    parser.add_argument(
        '--viewport-width',
        type=int,
        default=1920,
        help='Custom viewport width (default: 1920)'
    )
    
    parser.add_argument(
        '--viewport-height',
        type=int,
        default=1080,
        help='Custom viewport height (default: 1080)'
    )
    
    parser.add_argument(
        '--user-agent',
        type=str,
        help='Custom user agent string'
    )
    
    parser.add_argument(
        '--proxy',
        type=str,
        help='Proxy settings (e.g., http://proxy:8080)'
    )
    
    parser.add_argument(
        '--parallel',
        type=int,
        default=1,
        help='Number of parallel test executions (default: 1)'
    )
    
    parser.add_argument(
        '--retry',
        type=int,
        default=0,
        help='Number of retries for failed tests (default: 0)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Enhanced Testerat 2.0.0'
    )
    
    args = parser.parse_args()
    
    # Handle headless mode
    headless = args.headless and not args.no_headless
    
    # Create configuration
    config = UniversalTestConfig()
    
    # Load custom configuration if provided
    if args.config:
        try:
            with open(args.config, 'r') as f:
                custom_config = json.load(f)
                # Apply custom configuration
                for key, value in custom_config.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
            print(f"✅ Loaded custom configuration from {args.config}")
        except Exception as e:
            print(f"❌ Failed to load configuration: {e}")
            sys.exit(1)
    
    # Apply CLI arguments to configuration
    config.headless = headless
    config.detailed_logging = args.verbose
    config.test_authentication = not args.skip_auth
    config.test_workflows = not args.skip_workflows
    config.test_api_interactions = not args.skip_api
    config.test_security = not args.skip_security
    config.test_accessibility = not args.skip_accessibility
    config.test_performance = not args.skip_performance
    config.test_file_uploads = not args.skip_file_uploads
    config.test_modals_popups = not args.skip_modals
    
    # Apply enhanced CLI options
    config.timeout = args.timeout
    config.viewport_width = args.viewport_width
    config.viewport_height = args.viewport_height
    
    # Store additional options for browser configuration
    config.user_agent = args.user_agent
    config.proxy = args.proxy
    config.parallel_executions = args.parallel
    config.retry_count = args.retry
    
    # Set framework if specified
    if args.framework != 'auto':
        framework_map = {
            'react': FrameworkType.REACT,
            'vue': FrameworkType.VUE,
            'angular': FrameworkType.ANGULAR,
            'nextjs': FrameworkType.NEXTJS,
            'nuxtjs': FrameworkType.NUXTJS,
            'svelte': FrameworkType.SVELTE
        }
        config.framework = framework_map[args.framework]

    # Set output directory
    config.output_dir = args.output_dir

    # Print startup information
    print("🎯 Enhanced Testerat - Universal Web Testing Framework")
    print("=" * 60)
    print(f"🌐 Target URL: {args.url}")
    print(f"📝 Description: {args.description}")
    print(f"🖥️  Browser Mode: {'Headless' if headless else 'Visible'}")
    print(f"🔧 Framework: {args.framework}")
    print(f"📊 Output Directory: {args.output_dir}")
    print(f"⏱️  Timeout: {args.timeout}ms")
    print(f"📐 Viewport: {args.viewport_width}x{args.viewport_height}")
    if args.user_agent:
        print(f"🌐 User Agent: {args.user_agent}")
    if args.proxy:
        print(f"🔗 Proxy: {args.proxy}")
    if args.parallel > 1:
        print(f"⚡ Parallel Executions: {args.parallel}")
    if args.retry > 0:
        print(f"🔄 Retry Count: {args.retry}")
    print()

    # Show enabled test modules
    enabled_tests = []
    if config.test_authentication:
        enabled_tests.append("🔐 Authentication")
    if config.test_workflows:
        enabled_tests.append("🔄 Workflows")
    if config.test_api_interactions:
        enabled_tests.append("🌐 API")
    if config.test_file_uploads:
        enabled_tests.append("📁 File Uploads")
    if config.test_modals_popups:
        enabled_tests.append("🔲 Modals/Popups")
    if config.test_security:
        enabled_tests.append("🛡️ Security")
    if config.test_accessibility:
        enabled_tests.append("♿ Accessibility")
    if config.test_performance:
        enabled_tests.append("⚡ Performance")

    print("🧪 Enabled Test Modules:")
    for test in enabled_tests:
        print(f"   ✅ {test}")
    print()

    try:
        # Initialize and run Enhanced Testerat
        testerat = EnhancedTesterat(config)
        results = testerat.run_comprehensive_tests(args.url, args.description)

        print("🎉 Testing completed successfully!")
        print(f"📊 Results: {results.get('passed', 0)}/{results.get('total', 0)} tests passed")
        print(f"📄 Reports generated in: {args.output_dir}")

        # Exit with appropriate code
        if results.get('critical_issues', 0) > 0:
            sys.exit(2)  # Critical issues found
        elif results.get('failed', 0) > 0:
            sys.exit(1)  # Some tests failed
        else:
            sys.exit(0)  # All tests passed

    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
