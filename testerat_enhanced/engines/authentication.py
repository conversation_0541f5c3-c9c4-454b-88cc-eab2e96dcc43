"""
Universal Authentication Testing Engine

Tests authenticated user experiences across any web application framework.
Catches authentication state issues, session management problems, and access control bugs.

Universal Support:
- Any authentication system (NextAuth, Auth0, custom forms, OAuth)
- Any web framework (React, Vue, Angular, vanilla JS)
- Any session management approach (cookies, JWT, localStorage)
"""

import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from playwright.sync_api import Page, expect

from ..config.test_config import AuthConfig, AuthType
from ..utils.test_result import TestResult, TestSeverity
from ..utils.error_handling import Error<PERSON>and<PERSON>, ErrorSeverity as ErrSeverity, error_handler_decorator, safe_dom_operation


@dataclass
class AuthenticationState:
    """Universal authentication state representation"""
    is_authenticated: bool = False
    user_identifier: Optional[str] = None  # email, username, or ID
    session_data: Dict[str, Any] = None
    auth_method: Optional[AuthType] = None
    session_expires: Optional[float] = None
    csrf_token: Optional[str] = None


class AuthenticationEngine:
    """
    Universal Authentication Testing Engine
    
    Tests authentication flows and authenticated user experiences
    across any web application framework and authentication system.
    """
    
    def __init__(self, config: AuthConfig, logger: logging.Logger = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.error_handler = ErrorHandler(self.logger)
        self.current_auth_state = AuthenticationState()
        self.test_results: List[TestResult] = []
    
    def run_comprehensive_auth_tests(self, page: Page, base_url: str) -> List[TestResult]:
        """
        Run comprehensive authentication testing suite

        Enhanced universal tests that:
        1. First check if authentication system exists
        2. Adapt testing based on detected authentication type
        3. Skip tests gracefully when no auth system found
        4. Provide meaningful results for any web application
        """
        self.logger.info("🔐 Starting comprehensive authentication testing")
        self.test_results = []

        # Pre-check: Does this site have an authentication system?
        if not self._has_authentication_system(page, base_url):
            self.logger.info("No authentication system detected - skipping auth tests")

            # Add informational result
            no_auth_result = TestResult(
                test_name="authentication_system_detection",
                status="SKIPPED",
                details="No authentication system detected on this site",
                severity=TestSeverity.LOW.value,
                recommendations=[
                    "This appears to be a public site without user authentication",
                    "If authentication should be present, verify login forms and links are properly implemented"
                ],
                execution_time=0.0
            )
            self.test_results.append(no_auth_result)
            return self.test_results

        self.logger.info("Authentication system detected - proceeding with tests")

        # Test 1: Authentication Flow
        try:
            self.test_authentication_flow(page, base_url)
        except Exception as e:
            self.logger.warning(f"Authentication flow test failed: {e}")
            self._add_error_test_result("authentication_flow", str(e))

        # Test 2: Authentication State Consistency
        try:
            self.test_auth_state_consistency(page)
        except Exception as e:
            self.logger.warning(f"Auth state consistency test failed: {e}")
            self._add_error_test_result("auth_state_consistency", str(e))

        # Test 3: Protected Route Access (with enhanced discovery)
        try:
            self.test_protected_route_access(page, base_url)
        except Exception as e:
            self.logger.warning(f"Protected route access test failed: {e}")
            self._add_error_test_result("protected_route_access", str(e))

        # Test 4: Session Management
        try:
            self.test_session_management(page)
        except Exception as e:
            self.logger.warning(f"Session management test failed: {e}")
            self._add_error_test_result("session_management", str(e))

        # Test 5: Logout Flow
        try:
            self.test_logout_flow(page)
        except Exception as e:
            self.logger.warning(f"Logout flow test failed: {e}")
            self._add_error_test_result("logout_flow", str(e))

        # Test 6: Authentication Edge Cases
        try:
            self.test_auth_edge_cases(page, base_url)
        except Exception as e:
            self.logger.warning(f"Auth edge cases test failed: {e}")
            self._add_error_test_result("auth_edge_cases", str(e))

        self.logger.info(f"Authentication testing completed: {len(self.test_results)} tests run")
        return self.test_results

    def _add_error_test_result(self, test_name: str, error_message: str):
        """Add an error test result when a test fails to execute"""
        error_result = TestResult(
            test_name=test_name,
            status="FAILED",
            details=f"Test execution failed: {error_message}",
            severity=TestSeverity.MEDIUM.value,
            recommendations=["Review test configuration and site accessibility"],
            execution_time=0.0
        )
        self.test_results.append(error_result)
    
    def test_authentication_flow(self, page: Page, base_url: str) -> TestResult:
        """Test complete authentication flow - universal approach"""
        self.logger.info("Testing authentication flow")
        issues = []
        recommendations = []

        try:
            # First, check if the site has authentication at all
            if not self._has_authentication_system(page, base_url):
                # No authentication system detected - this is not an error for many sites
                return TestResult(
                    test_name="authentication_flow",
                    status="FAILED",
                    details="No custom authentication elements detected",
                    severity=TestSeverity.CRITICAL.value,
                    recommendations=["If this site should have authentication, verify login form elements are properly implemented"],
                    execution_time=0.0
                )

            # Check if user is already authenticated
            if self._is_user_authenticated(page):
                self.current_auth_state.is_authenticated = True
                self.current_auth_state.user_identifier = self._extract_user_identifier(page)
                return TestResult(
                    test_name="authentication_flow",
                    status="PASSED",
                    details="Authentication system detected and user is authenticated",
                    severity=TestSeverity.LOW.value,
                    recommendations=["Authentication system working properly"],
                    execution_time=0.0
                )

            # Try to navigate to login page to test authentication flow
            login_url = f"{base_url.rstrip('/')}{self.config.login_url}"
            try:
                page.goto(login_url, wait_until='networkidle')
            except:
                # If login URL doesn't exist, try to find login elements on current page
                pass

            # Detect authentication method
            auth_method = self._detect_auth_method(page)
            self.current_auth_state.auth_method = auth_method

            if auth_method == AuthType.FORM_BASED:
                result = self._test_form_based_auth(page, issues, recommendations)
            elif auth_method == AuthType.OAUTH:
                result = self._test_oauth_auth(page, issues, recommendations)
            else:
                # Authentication system detected but no specific method found
                issues.append("Authentication system detected but login method unclear")
                recommendations.append("Verify login form elements are accessible and properly labeled")

            # Verify authentication success
            if self._verify_authentication_success(page):
                self.current_auth_state.is_authenticated = True
                self.current_auth_state.user_identifier = self._extract_user_identifier(page)
            else:
                if not issues:  # Only add this if no other issues found
                    issues.append("Authentication flow completed but user not properly authenticated")
                    recommendations.append("Verify authentication success indicators and user state management")

        except Exception as e:
            issues.append(f"Authentication flow failed: {str(e)}")
            recommendations.append("Check login form accessibility and authentication endpoint")
        
        severity = TestSeverity.CRITICAL if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="authentication_flow",
            status=status,
            details="; ".join(issues) if issues else "Authentication flow successful",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )
        
        self.test_results.append(result)
        return result
    
    def test_auth_state_consistency(self, page: Page) -> TestResult:
        """
        Test authentication state consistency - catches useSession loading issues
        
        This is the critical test that catches issues like:
        - useSession loading state not handled properly
        - Authentication state flickering
        - Logged-out content shown to authenticated users
        """
        self.logger.info("Testing authentication state consistency")
        issues = []
        recommendations = []
        
        if not self.current_auth_state.is_authenticated:
            return TestResult(
                "auth_state_consistency", "SKIPPED",
                "Skipped - user not authenticated", TestSeverity.LOW.value, [], 0.0
            )
        
        try:
            # Test 1: Check for loading states that never resolve
            loading_indicators = self._find_loading_indicators(page)
            if loading_indicators:
                # Wait and check if loading states resolve
                time.sleep(2)
                persistent_loading = self._find_loading_indicators(page)
                if persistent_loading:
                    issues.append("Authentication loading state never resolves")
                    recommendations.append("Fix useSession or auth state loading handling")
            
            # Test 2: Check for logged-out content shown to authenticated users
            logged_out_content = self._find_logged_out_content(page)
            if logged_out_content:
                issues.append("Logged-out content visible to authenticated user")
                recommendations.append("Ensure authentication state is properly checked before rendering content")
            
            # Test 3: Check for authentication state flickering
            auth_indicators_before = self._get_auth_indicators(page)
            page.wait_for_timeout(1000)  # Wait 1 second
            auth_indicators_after = self._get_auth_indicators(page)
            
            if auth_indicators_before != auth_indicators_after:
                issues.append("Authentication state flickering detected")
                recommendations.append("Implement proper authentication state management to prevent flickering")
            
            # Test 4: Verify user-specific content is displayed
            user_content = self._find_user_specific_content(page)
            if not user_content:
                issues.append("No user-specific content found for authenticated user")
                recommendations.append("Ensure authenticated users see personalized content")
                
        except Exception as e:
            issues.append(f"Auth state consistency test failed: {str(e)}")
            recommendations.append("Review authentication state management implementation")
        
        severity = TestSeverity.CRITICAL if any("loading state never resolves" in issue for issue in issues) else \
                  TestSeverity.HIGH if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="auth_state_consistency",
            status=status,
            details="; ".join(issues) if issues else "Authentication state consistent",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )
        
        self.test_results.append(result)
        return result
    
    def test_protected_route_access(self, page: Page, base_url: str) -> TestResult:
        """
        Test access to protected routes with adaptive route discovery

        Enhanced testing that:
        1. Discovers actual routes instead of assuming hardcoded ones
        2. Only tests routes that actually exist
        3. Provides clear differentiation between route not found vs access denied
        """
        self.logger.info("Testing protected route access")
        issues = []
        recommendations = []
        routes_tested = 0

        # Discover actual protected routes if enabled
        if self.config.discover_routes:
            discovered_routes = self._discover_protected_routes(page, base_url)
            if discovered_routes:
                self.config.protected_routes.extend(discovered_routes)
                self.logger.info(f"Discovered {len(discovered_routes)} potential protected routes")

        # If no routes configured or discovered, try common patterns
        if not self.config.protected_routes:
            common_routes = ["/dashboard", "/profile", "/account", "/settings", "/admin", "/user"]
            existing_routes = []
            for route in common_routes:
                if self._check_route_exists(page, base_url, route):
                    existing_routes.append(route)

            if existing_routes:
                self.config.protected_routes = existing_routes
                self.logger.info(f"Found {len(existing_routes)} common protected routes")
            else:
                return TestResult(
                    test_name="protected_route_access",
                    status="SKIPPED",
                    details="No protected routes found to test",
                    severity=TestSeverity.LOW.value,
                    recommendations=["No protected routes detected - this may be expected for public sites"],
                    execution_time=0.0
                )

        # Test each discovered/configured route
        for route in self.config.protected_routes:
            try:
                # First check if route exists
                if not self._check_route_exists(page, base_url, route):
                    self.logger.debug(f"Route {route} does not exist, skipping")
                    continue

                routes_tested += 1
                protected_url = f"{base_url.rstrip('/')}{route}"
                response = page.goto(protected_url, wait_until='networkidle', timeout=10000)

                if self.current_auth_state.is_authenticated:
                    # Should have access
                    if self._is_redirected_to_login(page):
                        issues.append(f"Authenticated user redirected from protected route: {route}")
                        recommendations.append(f"Fix access control for {route}")
                    elif response and response.status >= 400:
                        issues.append(f"Authenticated user got {response.status} error for route: {route}")
                        recommendations.append(f"Check server-side access control for {route}")
                else:
                    # Should be redirected or blocked
                    if not self._is_redirected_to_login(page) and not self._is_access_denied(page) and (not response or response.status < 400):
                        issues.append(f"Unauthenticated access allowed to protected route: {route}")
                        recommendations.append(f"Implement proper access control for {route}")

            except Exception as e:
                # Don't treat timeouts or network errors as authentication failures
                if "timeout" in str(e).lower() or "net::" in str(e).lower():
                    self.logger.debug(f"Network/timeout error for route {route}: {e}")
                    continue
                else:
                    issues.append(f"Error testing protected route {route}: {str(e)}")
                    recommendations.append(f"Check route accessibility: {route}")

        # Provide meaningful results based on what was actually tested
        if routes_tested == 0:
            return TestResult(
                test_name="protected_route_access",
                status="SKIPPED",
                details="No accessible protected routes found to test",
                severity=TestSeverity.LOW.value,
                recommendations=["No testable protected routes found - this may be expected for public sites"],
                execution_time=0.0
            )

        severity = TestSeverity.HIGH if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        details = f"Tested {routes_tested} protected routes"
        if issues:
            details += f"; {'; '.join(issues)}"
        else:
            details += "; all routes properly secured"

        result = TestResult(
            test_name="protected_route_access",
            status=status,
            details=details,
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result
    
    def _has_authentication_system(self, page: Page, base_url: str) -> bool:
        """
        Check if the site has any authentication system

        Enhanced detection that checks for:
        1. Authentication form elements
        2. Authentication-related links and buttons
        3. Common authentication URLs
        4. Authentication-related meta tags and attributes
        5. User management indicators
        """
        try:
            self.logger.debug("Checking for authentication system...")

            # Method 1: Check current page for auth form elements
            auth_form_indicators = [
                'input[type="password"]', 'input[name*="password"]', 'input[id*="password"]',
                'input[name*="email"]', 'input[type="email"]', 'input[id*="email"]',
                'input[name*="username"]', 'input[id*="username"]',
                'form[action*="login"]', 'form[action*="auth"]', 'form[action*="signin"]'
            ]

            for indicator in auth_form_indicators:
                if page.query_selector(indicator):
                    self.logger.debug(f"Found auth form indicator: {indicator}")
                    return True

            # Method 2: Check for authenticated state indicators (user is already logged in)
            authenticated_indicators = [
                'button:has-text("Sign out")', 'button:has-text("Logout")', 'button:has-text("Log out")',
                'a:has-text("Sign out")', 'a:has-text("Logout")', 'a:has-text("Log out")',
                'a:has-text("Profile")', 'a:has-text("Account")', 'a:has-text("Dashboard")',
                'button:has-text("Profile")', 'button:has-text("Account")',
                '.user-menu', '.profile-menu', '.account-menu',
                '[data-testid*="logout"]', '[data-testid*="profile"]', '[data-testid*="user"]'
            ]

            for indicator in authenticated_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found authenticated state indicator: {indicator}")
                        return True
                except:
                    continue

            # Method 3: Check for authentication links and buttons (enhanced patterns)
            auth_link_indicators = [
                # Text-based selectors (more flexible)
                'a:has-text("Login")', 'a:has-text("Sign In")', 'a:has-text("Log In")', 'a:has-text("Sign in")',
                'button:has-text("Login")', 'button:has-text("Sign In")', 'button:has-text("Log In")', 'button:has-text("Sign in")',
                'button:has-text("Log in to your account")', 'a:has-text("Log in to your account")',
                'a:has-text("Register")', 'a:has-text("Sign Up")', 'a:has-text("Join")', 'a:has-text("Sign up")',
                'button:has-text("Register")', 'button:has-text("Sign Up")', 'button:has-text("Join")', 'button:has-text("Sign up")',
                # Class and attribute selectors
                '.login', '.signin', '.auth', '.register', '.signup',
                '[data-auth]', '[data-login]', '[data-signin]', '[data-signup]',
                # ARIA labels and accessibility
                '[aria-label*="login"]', '[aria-label*="sign in"]', '[aria-label*="sign up"]',
                '[title*="login"]', '[title*="sign in"]', '[title*="sign up"]'
            ]

            for indicator in auth_link_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found auth link indicator: {indicator}")
                        return True
                except:
                    continue

            # Method 3: Check for user management indicators
            user_indicators = [
                '.user-menu', '.profile-menu', '.account-menu',
                '[data-testid*="user"]', '[data-testid*="profile"]', '[data-testid*="account"]',
                'a[href*="/profile"]', 'a[href*="/account"]', 'a[href*="/dashboard"]',
                '.logout', '.signout', 'button:has-text("Logout")', 'a:has-text("Logout")'
            ]

            for indicator in user_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found user management indicator: {indicator}")
                        return True
                except:
                    continue

            # Method 4: Check page content for authentication keywords
            page_content = page.content().lower()
            auth_keywords = [
                'login', 'signin', 'sign in', 'log in', 'authenticate',
                'register', 'signup', 'sign up', 'create account',
                'forgot password', 'reset password', 'logout', 'sign out'
            ]

            keyword_count = sum(1 for keyword in auth_keywords if keyword in page_content)
            if keyword_count >= 2:  # Multiple auth keywords suggest auth system
                self.logger.debug(f"Found {keyword_count} authentication keywords in content")
                return True

            # Method 5: Check if common auth URLs exist (quick check)
            auth_urls = ['/login', '/signin', '/auth', '/register', '/signup']
            for auth_url in auth_urls:
                try:
                    # Quick check without full navigation
                    if f'href="{auth_url}"' in page_content or f"'{auth_url}'" in page_content:
                        self.logger.debug(f"Found reference to auth URL: {auth_url}")
                        return True
                except:
                    continue

            # Method 6: Check for OAuth/SSO indicators
            oauth_indicators = [
                'button:has-text("Google")', 'button:has-text("Facebook")', 'button:has-text("GitHub")',
                'a:has-text("Google")', 'a:has-text("Facebook")', 'a:has-text("GitHub")',
                '.oauth', '.sso', '[data-provider]', '.social-login'
            ]

            for indicator in oauth_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found OAuth/SSO indicator: {indicator}")
                        return True
                except:
                    continue

            self.logger.debug("No authentication system detected")
            return False

        except Exception as e:
            self.logger.debug(f"Error checking for authentication system: {e}")
            return False

    def _is_user_authenticated(self, page: Page) -> bool:
        """
        Check if user is currently authenticated by looking for authenticated state indicators
        """
        try:
            # Check for logout/signout buttons or links
            logout_indicators = [
                'button:has-text("Sign out")', 'button:has-text("Logout")', 'button:has-text("Log out")',
                'a:has-text("Sign out")', 'a:has-text("Logout")', 'a:has-text("Log out")',
                'button:has-text("Sign out of your account")', 'a:has-text("Sign out of your account")'
            ]

            for indicator in logout_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found logout indicator: {indicator}")
                        return True
                except:
                    continue

            # Check for user profile/account links
            profile_indicators = [
                'a:has-text("Profile")', 'a:has-text("Account")', 'a:has-text("Dashboard")',
                'a:has-text("View your profile")', 'a:has-text("My Account")', 'a:has-text("Settings")',
                'button:has-text("Profile")', 'button:has-text("Account")'
            ]

            for indicator in profile_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found profile indicator: {indicator}")
                        return True
                except:
                    continue

            # Check for user menu or navigation elements
            user_menu_indicators = [
                '.user-menu', '.profile-menu', '.account-menu', '.user-nav',
                '[data-testid*="user"]', '[data-testid*="profile"]', '[data-testid*="account"]'
            ]

            for indicator in user_menu_indicators:
                try:
                    if page.query_selector(indicator):
                        self.logger.debug(f"Found user menu indicator: {indicator}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Error checking authentication state: {e}")
            return False

    def _detect_auth_method(self, page: Page) -> AuthType:
        """Universal authentication method detection"""
        # Check for OAuth buttons
        oauth_indicators = page.query_selector_all(
            'button:has-text("Google"), button:has-text("GitHub"), button:has-text("OAuth"), .oauth-btn'
        )
        if oauth_indicators:
            return AuthType.OAUTH

        # Check for form-based auth
        email_input = page.query_selector(self.config.selectors["email_input"])
        password_input = page.query_selector(self.config.selectors["password_input"])
        if email_input and password_input:
            return AuthType.FORM_BASED

        # Check for NextAuth indicators
        if page.query_selector('[data-provider]') or 'next-auth' in page.url:
            return AuthType.NEXTAUTH

        return AuthType.CUSTOM
    
    def _test_form_based_auth(self, page: Page, issues: List[str], recommendations: List[str]) -> bool:
        """Test form-based authentication"""
        try:
            # Fill login form
            email_input = page.query_selector(self.config.selectors["email_input"])
            password_input = page.query_selector(self.config.selectors["password_input"])
            login_button = page.query_selector(self.config.selectors["login_button"])
            
            if not email_input:
                issues.append("Email input field not found")
                return False
            if not password_input:
                issues.append("Password input field not found")
                return False
            if not login_button:
                issues.append("Login button not found")
                return False
            
            # Use test credentials
            test_user = self.config.test_users["standard"]
            email_input.fill(test_user["email"])
            password_input.fill(test_user["password"])
            
            # Submit form
            login_button.click()
            page.wait_for_load_state('networkidle')
            
            return True
            
        except Exception as e:
            issues.append(f"Form-based authentication failed: {str(e)}")
            return False
    
    def _verify_authentication_success(self, page: Page) -> bool:
        """Universal authentication success verification"""
        # Check for authentication indicators
        auth_indicator = page.query_selector(self.config.selectors["auth_indicator"])
        if auth_indicator:
            return True
        
        # Check if redirected away from login
        if self.config.login_url not in page.url:
            return True
        
        # Check for user menu
        user_menu = page.query_selector(self.config.selectors["user_menu"])
        if user_menu:
            return True
        
        return False

    def _check_ui_auth_state_update(self, page: Page) -> bool:
        """Check if UI properly updates to show authenticated state"""
        try:
            # Check if login/signup buttons are still visible (they shouldn't be)
            login_buttons = [
                'button:has-text("Log in")', 'button:has-text("Login")', 'button:has-text("Sign in")',
                'a:has-text("Log in")', 'a:has-text("Login")', 'a:has-text("Sign in")',
                'button:has-text("Log in to your account")', 'a:has-text("Log in to your account")'
            ]

            for button_selector in login_buttons:
                try:
                    if page.query_selector(button_selector):
                        self.logger.debug(f"Found login button after auth: {button_selector}")
                        return False  # Login button still visible = UI not updated
                except:
                    continue

            # Check if logout/profile buttons are now visible (they should be)
            logout_buttons = [
                'button:has-text("Sign out")', 'button:has-text("Logout")', 'button:has-text("Log out")',
                'a:has-text("Sign out")', 'a:has-text("Logout")', 'a:has-text("Log out")',
                'a:has-text("Profile")', 'a:has-text("Account")', 'a:has-text("Dashboard")'
            ]

            for button_selector in logout_buttons:
                try:
                    if page.query_selector(button_selector):
                        self.logger.debug(f"Found logout/profile button: {button_selector}")
                        return True  # Logout/profile button visible = UI updated
                except:
                    continue

            return False  # No authenticated UI elements found

        except Exception as e:
            self.logger.debug(f"Error checking UI auth state: {e}")
            return False

    def _test_session_persistence(self, page: Page) -> bool:
        """Test if authentication session persists across navigation"""
        try:
            # Try to navigate to a protected route
            original_url = page.url

            # Test dashboard route
            page.goto(f"{page.url.split('/')[0]}//{page.url.split('/')[2]}/dashboard")
            page.wait_for_timeout(3000)

            # Check if we're still on dashboard (not redirected to login)
            current_url = page.url
            if '/login' in current_url:
                self.logger.debug("Session not persistent - redirected to login")
                return False

            if '/dashboard' in current_url:
                self.logger.debug("Session persistent - stayed on dashboard")
                return True

            # Go back to original page
            page.goto(original_url)
            return False

        except Exception as e:
            self.logger.debug(f"Error testing session persistence: {e}")
            return False

    def _find_loading_indicators(self, page: Page) -> List[str]:
        """
        Find generic loading indicators on the page

        IMPORTANT: This function detects generic loading indicators, NOT React useSession specifically.
        It looks for common loading patterns that could indicate:
        - Authentication state loading
        - General page loading
        - Component loading states
        - Any spinner or loading UI

        This is NOT specific to useSession hook behavior or React/NextAuth.
        """
        loading_selectors = [
            '.loading', '.spinner', '[data-testid="loading"]',
            'text="Loading..."', 'text="Authenticating..."',
            '.skeleton', '.auth-loading'
        ]

        found_loading = []
        for selector in loading_selectors:
            elements = page.query_selector_all(selector)
            if elements:
                found_loading.append(selector)

        return found_loading
    
    def _find_logged_out_content(self, page: Page) -> List[str]:
        """Find content that should only be visible to logged-out users"""
        logged_out_selectors = [
            'text="Sign In"', 'text="Log In"', 'text="Login"',
            '.login-prompt', '.auth-required', 'text="Please log in"'
        ]
        
        found_content = []
        for selector in logged_out_selectors:
            elements = page.query_selector_all(selector)
            if elements:
                found_content.append(selector)
        
        return found_content

    def _get_auth_indicators(self, page: Page) -> Dict[str, bool]:
        """Get current authentication indicators for state comparison"""
        return {
            "auth_indicator": bool(page.query_selector(self.config.selectors["auth_indicator"])),
            "user_menu": bool(page.query_selector(self.config.selectors["user_menu"])),
            "logout_button": bool(page.query_selector(self.config.selectors["logout_button"])),
            "login_form": bool(page.query_selector(self.config.selectors["email_input"]))
        }

    def _find_user_specific_content(self, page: Page) -> List[str]:
        """Find user-specific content that should be visible to authenticated users"""
        user_content_selectors = [
            '.user-name', '.username', '.profile-info',
            '[data-testid="user-info"]', '.dashboard', '.user-dashboard'
        ]

        found_content = []
        for selector in user_content_selectors:
            elements = page.query_selector_all(selector)
            if elements:
                found_content.append(selector)

        return found_content

    def _extract_user_identifier(self, page: Page) -> Optional[str]:
        """Extract user identifier from authenticated page"""
        # Try to find user email or name
        selectors = [
            '.user-email', '.user-name', '.username',
            '[data-testid="user-email"]', '[data-testid="user-name"]'
        ]

        for selector in selectors:
            element = page.query_selector(selector)
            if element:
                text = element.text_content()
                if text and text.strip():
                    return text.strip()

        return None

    def _is_redirected_to_login(self, page: Page) -> bool:
        """Check if page was redirected to login"""
        return self.config.login_url in page.url or 'login' in page.url.lower()

    def _is_access_denied(self, page: Page) -> bool:
        """Check if access was denied"""
        denied_indicators = [
            'text="Access Denied"', 'text="Unauthorized"', 'text="403"',
            '.access-denied', '.unauthorized', '.error-403'
        ]

        for selector in denied_indicators:
            if page.query_selector(selector):
                return True

        return False

    def _discover_protected_routes(self, page: Page, base_url: str) -> List[str]:
        """
        Discover protected routes by scanning navigation and links

        Enhanced route discovery that:
        1. Scans navigation menus for protected links
        2. Analyzes link text and attributes for authentication indicators
        3. Checks common protected route patterns
        4. Validates route accessibility
        5. Filters out public routes
        """
        discovered_routes = []

        try:
            self.logger.debug("Starting route discovery...")

            # Method 1: Scan navigation links
            nav_selectors = [
                'nav a[href]', '.navigation a[href]', '.nav a[href]',
                '.menu a[href]', '.sidebar a[href]', '[role="navigation"] a[href]',
                '.header a[href]', '.navbar a[href]', '.topbar a[href]'
            ]

            for selector in nav_selectors:
                try:
                    links = page.query_selector_all(selector)
                    for link in links:
                        href = link.get_attribute('href')
                        if href and href.startswith('/') and len(href) > 1:
                            # Get link text and analyze
                            link_text = (link.text_content() or '').lower().strip()

                            # Check for protected keywords in link text
                            protected_keywords = [
                                'dashboard', 'profile', 'account', 'settings', 'admin',
                                'user', 'my ', 'personal', 'private', 'secure', 'panel',
                                'management', 'control', 'edit', 'manage', 'preferences'
                            ]

                            # Check for protected patterns in href
                            protected_patterns = [
                                '/dashboard', '/profile', '/account', '/settings', '/admin',
                                '/user', '/my', '/personal', '/private', '/secure', '/panel'
                            ]

                            is_protected = False

                            # Check text content
                            if any(keyword in link_text for keyword in protected_keywords):
                                is_protected = True

                            # Check href patterns
                            if any(pattern in href.lower() for pattern in protected_patterns):
                                is_protected = True

                            # Check for authentication-related attributes
                            if link.get_attribute('data-auth') or link.get_attribute('data-protected'):
                                is_protected = True

                            if is_protected and href not in discovered_routes:
                                discovered_routes.append(href)
                                self.logger.debug(f"Discovered protected route: {href} (text: '{link_text}')")

                except Exception as e:
                    self.logger.debug(f"Error scanning selector {selector}: {e}")

            # Method 2: Look for form actions that might be protected
            try:
                forms = page.query_selector_all('form[action]')
                for form in forms:
                    action = form.get_attribute('action')
                    if action and action.startswith('/') and len(action) > 1:
                        # Skip authentication forms
                        if not any(auth_term in action.lower() for auth_term in ['login', 'signin', 'auth', 'register', 'signup']):
                            # Check if form has protected indicators
                            form_classes = form.get_attribute('class') or ''
                            form_id = form.get_attribute('id') or ''

                            if any(term in (form_classes + form_id).lower() for term in ['admin', 'user', 'profile', 'settings']):
                                if action not in discovered_routes:
                                    discovered_routes.append(action)
                                    self.logger.debug(f"Discovered protected form action: {action}")

            except Exception as e:
                self.logger.debug(f"Error scanning forms: {e}")

            # Method 3: Check common protected route patterns
            common_protected_routes = [
                '/dashboard', '/profile', '/account', '/settings', '/admin',
                '/user', '/my-account', '/my-profile', '/preferences', '/panel',
                '/management', '/control-panel', '/user-dashboard'
            ]

            for route in common_protected_routes:
                if route not in discovered_routes:
                    # Only add if we can verify it might exist
                    if self._route_might_exist(page, base_url, route):
                        discovered_routes.append(route)
                        self.logger.debug(f"Added common protected route: {route}")

            # Filter out definitely public routes
            public_routes = [
                '/', '/home', '/login', '/signin', '/signup', '/register',
                '/about', '/contact', '/help', '/faq', '/terms', '/privacy',
                '/blog', '/news', '/products', '/services', '/pricing'
            ]

            discovered_routes = [route for route in discovered_routes if route not in public_routes]

            # Remove duplicates and limit
            discovered_routes = list(dict.fromkeys(discovered_routes))[:10]

            self.logger.info(f"Route discovery completed: found {len(discovered_routes)} potential protected routes")

        except Exception as e:
            self.logger.warning(f"Route discovery failed: {e}")

        return discovered_routes

    def _route_might_exist(self, page: Page, base_url: str, route: str) -> bool:
        """
        Quick check if a route might exist without full navigation

        Uses heuristics to avoid testing routes that definitely don't exist
        """
        try:
            # Check if there are any links pointing to this route
            route_links = page.query_selector_all(f'a[href="{route}"], a[href*="{route}"]')
            if route_links:
                return True

            # Check if route pattern appears in page content
            page_content = page.content()
            if route in page_content:
                return True

            # For admin routes, check if there are admin indicators
            if 'admin' in route.lower():
                admin_indicators = page.query_selector_all('.admin, [data-admin], #admin')
                return len(admin_indicators) > 0

            # For user routes, check if there are user indicators
            if any(term in route.lower() for term in ['user', 'profile', 'account']):
                user_indicators = page.query_selector_all('.user, .profile, .account, [data-user]')
                return len(user_indicators) > 0

            return False

        except Exception:
            return False

    def _check_route_exists(self, page: Page, base_url: str, route: str) -> bool:
        """
        Check if a route exists without affecting the current page state

        Enhanced route checking that:
        1. Uses HEAD requests when possible for efficiency
        2. Handles redirects appropriately
        3. Distinguishes between 404 (doesn't exist) and 403 (protected)
        4. Restores page state safely
        """
        try:
            # First try a quick HEAD request if requests is available
            try:
                import requests
                test_url = f"{base_url.rstrip('/')}{route}"

                # Use HEAD request for efficiency
                response = requests.head(test_url, timeout=5, allow_redirects=True)

                # Route exists if status < 400 or if it's a 403 (protected but exists)
                if response.status_code < 400 or response.status_code == 403:
                    self.logger.debug(f"Route {route} exists (status: {response.status_code})")
                    return True
                elif response.status_code == 404:
                    self.logger.debug(f"Route {route} not found (404)")
                    return False
                else:
                    # For other status codes, fall back to browser check
                    self.logger.debug(f"Route {route} returned {response.status_code}, checking with browser")

            except ImportError:
                # requests not available, use browser method
                pass
            except Exception as e:
                # Network error or other issue, fall back to browser method
                self.logger.debug(f"HEAD request failed for {route}: {e}")

            # Fallback: Use browser navigation
            original_url = page.url

            try:
                test_url = f"{base_url.rstrip('/')}{route}"
                response = page.goto(test_url, wait_until='domcontentloaded', timeout=5000)

                if response:
                    status = response.status

                    # Route exists if:
                    # - Status < 400 (success or redirect)
                    # - Status 403 (forbidden but exists)
                    # - Status 401 (unauthorized but exists)
                    exists = status < 400 or status in [401, 403]

                    self.logger.debug(f"Route {route} browser check: status {status}, exists: {exists}")

                    # Restore original page
                    try:
                        if original_url != test_url:
                            page.goto(original_url, wait_until='domcontentloaded', timeout=5000)
                    except Exception as restore_error:
                        self.logger.debug(f"Failed to restore original page: {restore_error}")

                    return exists
                else:
                    self.logger.debug(f"No response received for route {route}")
                    return False

            except Exception as nav_error:
                self.logger.debug(f"Browser navigation failed for {route}: {nav_error}")

                # Try to restore original page even if navigation failed
                try:
                    page.goto(original_url, wait_until='domcontentloaded', timeout=5000)
                except:
                    pass

                return False

        except Exception as e:
            self.logger.debug(f"Route existence check failed for {route}: {e}")
            return False

    def test_session_management(self, page: Page) -> TestResult:
        """Test session management and persistence"""
        self.logger.info("Testing session management")
        issues = []
        recommendations = []

        if not self.current_auth_state.is_authenticated:
            return TestResult(
                "session_management", "SKIPPED",
                "Skipped - user not authenticated", TestSeverity.LOW.value, [], 0.0
            )

        try:
            # Test session persistence across page reloads
            original_url = page.url
            page.reload(wait_until='networkidle')

            if not self._verify_authentication_success(page):
                issues.append("Session not persisted across page reload")
                recommendations.append("Implement proper session persistence")

            # Test CSRF token if required
            if self.config.csrf_token_required:
                csrf_token = self._extract_csrf_token(page)
                if not csrf_token:
                    issues.append("CSRF token not found")
                    recommendations.append("Implement CSRF protection")
                else:
                    self.current_auth_state.csrf_token = csrf_token

        except Exception as e:
            issues.append(f"Session management test failed: {str(e)}")
            recommendations.append("Review session management implementation")

        severity = TestSeverity.HIGH if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="session_management",
            status=status,
            details="; ".join(issues) if issues else "Session management working properly",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result

    def test_logout_flow(self, page: Page) -> TestResult:
        """Test logout functionality"""
        self.logger.info("Testing logout flow")
        issues = []
        recommendations = []

        if not self.current_auth_state.is_authenticated:
            return TestResult(
                "logout_flow", "SKIPPED",
                "Skipped - user not authenticated", TestSeverity.LOW.value, [], 0.0
            )

        try:
            # Find and click logout button
            logout_button = page.query_selector(self.config.selectors["logout_button"])
            if not logout_button:
                issues.append("Logout button not found")
                recommendations.append("Ensure logout functionality is accessible")
            else:
                logout_button.click()
                page.wait_for_load_state('networkidle')

                # Verify logout success
                if self._verify_authentication_success(page):
                    issues.append("User still appears authenticated after logout")
                    recommendations.append("Fix logout functionality and session cleanup")
                else:
                    self.current_auth_state.is_authenticated = False
                    self.current_auth_state.user_identifier = None

        except Exception as e:
            issues.append(f"Logout test failed: {str(e)}")
            recommendations.append("Review logout implementation")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="logout_flow",
            status=status,
            details="; ".join(issues) if issues else "Logout flow working properly",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result

    def test_auth_edge_cases(self, page: Page, base_url: str) -> TestResult:
        """Test authentication edge cases"""
        self.logger.info("Testing authentication edge cases")
        issues = []
        recommendations = []

        try:
            # Test invalid credentials
            login_url = f"{base_url.rstrip('/')}{self.config.login_url}"
            page.goto(login_url, wait_until='networkidle')

            # Try invalid login
            if self._test_invalid_credentials(page):
                issues.append("Invalid credentials accepted")
                recommendations.append("Implement proper credential validation")

            # Test empty form submission
            if self._test_empty_form_submission(page):
                issues.append("Empty form submission accepted")
                recommendations.append("Add form validation for required fields")

        except Exception as e:
            issues.append(f"Auth edge case testing failed: {str(e)}")
            recommendations.append("Review authentication edge case handling")

        severity = TestSeverity.MEDIUM if issues else TestSeverity.LOW
        status = "FAILED" if issues else "PASSED"

        result = TestResult(
            test_name="auth_edge_cases",
            status=status,
            details="; ".join(issues) if issues else "Authentication edge cases handled properly",
            severity=severity.value,
            recommendations=recommendations,
            execution_time=0.0
        )

        self.test_results.append(result)
        return result

    def _extract_csrf_token(self, page: Page) -> Optional[str]:
        """Extract CSRF token from page"""
        for selector in self.config.csrf_token_selectors:
            element = page.query_selector(selector)
            if element:
                token = element.get_attribute('content') or element.get_attribute('value')
                if token:
                    return token
        return None

    def _test_invalid_credentials(self, page: Page) -> bool:
        """Test with invalid credentials"""
        try:
            email_input = page.query_selector(self.config.selectors["email_input"])
            password_input = page.query_selector(self.config.selectors["password_input"])
            login_button = page.query_selector(self.config.selectors["login_button"])

            if email_input and password_input and login_button:
                email_input.fill("<EMAIL>")
                password_input.fill("wrongpassword")
                login_button.click()
                page.wait_for_load_state('networkidle')

                # Should not be authenticated
                return self._verify_authentication_success(page)
        except Exception as e:
            self.logger.debug(f"Invalid credentials test failed: {e}")
        return False

    def _test_empty_form_submission(self, page: Page) -> bool:
        """Test empty form submission"""
        try:
            email_input = page.query_selector(self.config.selectors["email_input"])
            password_input = page.query_selector(self.config.selectors["password_input"])
            login_button = page.query_selector(self.config.selectors["login_button"])

            if email_input and password_input and login_button:
                email_input.fill("")
                password_input.fill("")
                login_button.click()
                page.wait_for_load_state('networkidle')

                # Should not be authenticated
                return self._verify_authentication_success(page)
        except Exception as e:
            self.logger.debug(f"Empty form submission test failed: {e}")
        return False

    def _test_oauth_auth(self, page: Page, issues: List[str], recommendations: List[str]) -> bool:
        """
        Test OAuth authentication flows

        This function provides basic OAuth testing by:
        - Detecting OAuth login buttons/links
        - Testing OAuth redirect flows
        - Checking for OAuth error handling
        - Validating OAuth callback handling

        Note: Full OAuth testing requires provider-specific implementation.
        """
        try:
            # Look for OAuth login buttons
            oauth_selectors = [
                'text="Sign in with Google"', 'text="Login with Google"',
                'text="Sign in with GitHub"', 'text="Login with GitHub"',
                'text="Continue with Facebook"', 'text="Sign in with Microsoft"',
                '.oauth-button', '.social-login', '[data-provider]'
            ]

            oauth_buttons = []
            for selector in oauth_selectors:
                elements = page.query_selector_all(selector)
                oauth_buttons.extend(elements)

            if not oauth_buttons:
                issues.append("No OAuth login options detected")
                recommendations.append("Consider adding OAuth authentication options for better user experience")
                return False

            # Test first OAuth button
            first_oauth_button = oauth_buttons[0]
            button_text = first_oauth_button.text_content() or "OAuth button"

            # Check if button is functional
            if first_oauth_button.is_disabled():
                issues.append(f"OAuth button '{button_text}' is disabled")
                recommendations.append("Ensure OAuth buttons are functional and properly configured")
                return False

            # Test OAuth redirect (without actually completing OAuth flow)
            original_url = page.url
            first_oauth_button.click()
            page.wait_for_timeout(3000)  # Wait for potential redirect

            current_url = page.url
            if current_url == original_url:
                issues.append(f"OAuth button '{button_text}' did not trigger redirect")
                recommendations.append("Verify OAuth provider configuration and redirect URLs")
                return False

            # Check if redirected to OAuth provider
            oauth_domains = ['accounts.google.com', 'github.com', 'facebook.com', 'login.microsoftonline.com']
            is_oauth_redirect = any(domain in current_url for domain in oauth_domains)

            if not is_oauth_redirect:
                issues.append(f"OAuth redirect to unexpected URL: {current_url}")
                recommendations.append("Verify OAuth provider configuration")

            # Navigate back for further testing
            page.goto(original_url, wait_until='networkidle')

            return True

        except Exception as e:
            issues.append(f"OAuth testing failed: {str(e)}")
            recommendations.append("Review OAuth implementation and error handling")
            return False

    def _test_generic_auth(self, page: Page, issues: List[str], recommendations: List[str]) -> bool:
        """
        Test generic/custom authentication methods

        This function provides basic testing for custom authentication by:
        - Detecting custom login forms and methods
        - Testing basic form validation
        - Checking for authentication feedback
        - Validating error handling
        """
        try:
            # Look for custom authentication elements
            custom_auth_indicators = [
                '.custom-login', '.auth-form', '.signin-form',
                '[data-testid="auth"]', '.authentication',
                'input[name="username"]', 'input[name="user"]'
            ]

            custom_elements = []
            for selector in custom_auth_indicators:
                elements = page.query_selector_all(selector)
                custom_elements.extend(elements)

            if not custom_elements:
                issues.append("No custom authentication elements detected")
                recommendations.append("Verify authentication method or implement standard login forms")
                return False

            # Test basic form functionality
            username_input = page.query_selector('input[name="username"], input[name="user"], input[type="text"]')
            password_input = page.query_selector('input[name="password"], input[type="password"]')
            submit_button = page.query_selector('button[type="submit"], input[type="submit"], .submit-btn')

            if not (username_input and password_input and submit_button):
                issues.append("Custom authentication form incomplete - missing required fields")
                recommendations.append("Ensure authentication form has username, password, and submit elements")
                return False

            # Test form validation with empty submission
            original_url = page.url
            submit_button.click()
            page.wait_for_timeout(2000)

            # Check for validation messages
            validation_selectors = [
                '.error', '.validation-error', '.field-error',
                'text="Required"', 'text="This field is required"'
            ]

            has_validation = False
            for selector in validation_selectors:
                if page.query_selector(selector):
                    has_validation = True
                    break

            if not has_validation:
                issues.append("Custom authentication form lacks proper validation")
                recommendations.append("Add form validation for required fields")

            # Test with invalid credentials
            username_input.fill("testuser")
            password_input.fill("wrongpassword")
            submit_button.click()
            page.wait_for_timeout(3000)

            # Check for error handling
            error_selectors = [
                '.auth-error', '.login-error', 'text="Invalid credentials"',
                'text="Login failed"', '.error-message'
            ]

            has_error_handling = False
            for selector in error_selectors:
                if page.query_selector(selector):
                    has_error_handling = True
                    break

            if not has_error_handling:
                issues.append("Custom authentication lacks proper error handling")
                recommendations.append("Implement clear error messages for authentication failures")

            return True

        except Exception as e:
            issues.append(f"Custom authentication testing failed: {str(e)}")
            recommendations.append("Review custom authentication implementation")
            return False
