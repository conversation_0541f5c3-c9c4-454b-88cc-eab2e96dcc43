{"metadata": {"report_id": "20250617_154155", "generated_at": "2025-06-17T15:41:55.052158", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "Unknown Application", "version": null, "features": [], "confidence": 0.6, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "Unknown Application", "url": "https://httpbin.org/html"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/html", "total_tests": 5, "passed": 4, "failed": 1, "critical_issues": 0, "success_rate": 80.0, "total_execution_time": 43.71597194671631, "start_time": "2025-06-17T15:41:55.052405", "end_time": "2025-06-17T15:42:38.768375"}, "test_results": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:42:35.990138", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:42:38.753664", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T15:42:38.753691", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T15:42:38.763734", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T15:42:38.768364", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:42:35.990138", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Security basics look good - consider comprehensive security audit", "Implement proper access control for /profile", "Implement proper access control for /dashboard", "Performance looks good - continue monitoring", "Accessibility basics look good - consider comprehensive accessibility audit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 2, "passed": 1, "failed": 1}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"HIGH": 1, "LOW": 4}, "avg_execution_time": 0.0, "slowest_test": "protected_route_access", "fastest_test": "protected_route_access"}}