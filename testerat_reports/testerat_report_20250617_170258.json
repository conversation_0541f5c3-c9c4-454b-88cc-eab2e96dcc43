{"metadata": {"report_id": "20250617_170258", "generated_at": "2025-06-17T17:02:58.532139", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.UNKNOWN", "app_name": "GitHub · Build and ship software on a single, collaborative platform · GitHub", "version": null, "features": ["test-ids", "forms", "interactive-elements", "modals", "navigation", "data-visualization", "accessibility-labels", "aria-roles"], "confidence": 0.1, "detection_methods": ["DOM analysis"], "detected_patterns": []}, "app_info": {"name": "GitHub · Build and ship software on a single, collaborative platform · GitHub", "url": "https://github.com"}}, "summary": {"suite_name": "Enhanced Testerat - https://github.com", "total_tests": 3, "passed": 1, "failed": 1, "critical_issues": 0, "success_rate": 33.33333333333333, "total_execution_time": 3.0850930213928223, "start_time": "2025-06-17T17:02:58.532195", "end_time": "2025-06-17T17:03:01.617284"}, "test_results": [{"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T17:03:01.570909", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "FAILED", "details": "7 form inputs without proper labels", "severity": "MEDIUM", "recommendations": ["Associate labels with form inputs"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T17:03:01.616036", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "WARNING", "details": "High resource count: 135 resources", "severity": "MEDIUM", "recommendations": ["Reduce number of HTTP requests - bundle resources or use HTTP/2"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T17:03:01.617275", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "basic_accessibility", "status": "FAILED", "details": "7 form inputs without proper labels", "severity": "MEDIUM", "recommendations": ["Associate labels with form inputs"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T17:03:01.616036", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Security basics look good - consider comprehensive security audit", "Associate labels with form inputs", "Reduce number of HTTP requests - bundle resources or use HTTP/2"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 0, "failed": 1}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 1, "MEDIUM": 2}, "avg_execution_time": 0.0, "slowest_test": "basic_security", "fastest_test": "basic_security"}}