{"metadata": {"report_id": "20250617_164055", "generated_at": "2025-06-17T16:40:55.225744", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "Wikipedia", "version": null, "features": ["forms", "interactive-elements", "navigation", "accessibility-labels", "aria-roles"], "confidence": 0.6, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "Wikipedia", "url": "https://www.wikipedia.org"}}, "summary": {"suite_name": "Enhanced Testerat - https://www.wikipedia.org", "total_tests": 8, "passed": 5, "failed": 2, "critical_issues": 0, "success_rate": 62.5, "total_execution_time": 9.385726928710938, "start_time": "2025-06-17T16:40:55.225889", "end_time": "2025-06-17T16:41:04.611615"}, "test_results": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:41:01.402400", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:41:01.988764", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:41:02.027654", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:41:02.052333", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:41:04.610810", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:41:04.611392", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:41:04.611574", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T16:41:04.611606", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:41:01.402400", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:41:02.052333", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile", "Add workflow testing if multi-step processes exist", "Implement CSRF protection for form submissions", "Security basics look good - consider comprehensive security audit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 2, "passed": 1, "failed": 1}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 3, "failed": 1}, "security": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"HIGH": 1, "LOW": 6, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "protected_route_access", "fastest_test": "protected_route_access"}}