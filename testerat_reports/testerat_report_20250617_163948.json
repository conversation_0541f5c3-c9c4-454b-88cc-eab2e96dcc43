{"metadata": {"report_id": "20250617_163948", "generated_at": "2025-06-17T16:39:48.114774", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.SVELTE", "app_name": "Newest Questions - Stack Overflow", "version": null, "features": ["test-ids", "forms", "interactive-elements", "modals", "navigation", "accessibility-labels", "aria-roles"], "confidence": 1.0, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "Newest Questions - Stack Overflow", "url": "https://stackoverflow.com"}}, "summary": {"suite_name": "Enhanced Testerat - https://stackoverflow.com", "total_tests": 9, "passed": 3, "failed": 5, "critical_issues": 1, "success_rate": 33.33333333333333, "total_execution_time": 67.057541847229, "start_time": "2025-06-17T16:39:48.114938", "end_time": "2025-06-17T16:40:55.172506"}, "test_results": [{"test_name": "authentication_flow", "status": "FAILED", "details": "OAuth testing failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "CRITICAL", "recommendations": ["Review OAuth implementation and error handling"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:22.265758", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:24.192593", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "FAILED", "details": "Auth edge case testing failed: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://stackoverflow.com/login\", waiting until \"networkidle\"\n", "severity": "MEDIUM", "recommendations": ["Review authentication edge case handling"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:54.197314", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:40:54.276109", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:40:54.292096", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Client error on /api/auth/signin: 429; Client error on /api/auth/signout: 429; Client error on /api/csrf-token: 429; Client error on /api/user/profile: 429; Client error on /api/forms/submit: 429", "severity": "MEDIUM", "recommendations": ["Check endpoint implementation: /api/auth/signin", "Check endpoint implementation: /api/auth/signout", "Check endpoint implementation: /api/csrf-token", "Check endpoint implementation: /api/user/profile", "Check endpoint implementation: /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:40:55.171220", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:40:55.172208", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:40:55.172419", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T16:40:55.172469", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [{"test_name": "authentication_flow", "status": "FAILED", "details": "OAuth testing failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "CRITICAL", "recommendations": ["Review OAuth implementation and error handling"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:22.265758", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "failures": [{"test_name": "authentication_flow", "status": "FAILED", "details": "OAuth testing failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "CRITICAL", "recommendations": ["Review OAuth implementation and error handling"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:22.265758", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:24.192593", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "FAILED", "details": "Auth edge case testing failed: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://stackoverflow.com/login\", waiting until \"networkidle\"\n", "severity": "MEDIUM", "recommendations": ["Review authentication edge case handling"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:40:54.197314", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:40:54.292096", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Client error on /api/auth/signin: 429; Client error on /api/auth/signout: 429; Client error on /api/csrf-token: 429; Client error on /api/user/profile: 429; Client error on /api/forms/submit: 429", "severity": "MEDIUM", "recommendations": ["Check endpoint implementation: /api/auth/signin", "Check endpoint implementation: /api/auth/signout", "Check endpoint implementation: /api/csrf-token", "Check endpoint implementation: /api/user/profile", "Check endpoint implementation: /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:40:55.171220", "framework_detected": "svelte", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile", "Add workflow testing if multi-step processes exist", "Review OAuth implementation and error handling", "Implement CSRF protection for form submissions", "Check endpoint implementation: /api/forms/submit", "Security basics look good - consider comprehensive security audit", "Check endpoint implementation: /api/user/profile", "Check endpoint implementation: /api/csrf-token", "Review authentication edge case handling", "Check endpoint implementation: /api/auth/signin", "Check endpoint implementation: /api/auth/signout"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 3, "passed": 0, "failed": 3}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 2, "failed": 2}, "security": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"CRITICAL": 1, "HIGH": 1, "MEDIUM": 3, "LOW": 4}, "avg_execution_time": 0.0, "slowest_test": "authentication_flow", "fastest_test": "authentication_flow"}}