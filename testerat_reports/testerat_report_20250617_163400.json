{"metadata": {"report_id": "20250617_163400", "generated_at": "2025-06-17T16:34:00.208530", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "GitHub · Build and ship software on a single, collaborative platform · GitHub", "version": null, "features": ["test-ids", "forms", "interactive-elements", "modals", "navigation", "data-visualization", "accessibility-labels", "aria-roles"], "confidence": 0.6, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "GitHub · Build and ship software on a single, collaborative platform · GitHub", "url": "https://github.com"}}, "summary": {"suite_name": "Enhanced Testerat - https://github.com", "total_tests": 18, "passed": 7, "failed": 11, "critical_issues": 1, "success_rate": 38.88888888888889, "total_execution_time": 224.11057424545288, "start_time": "2025-06-17T16:34:00.208655", "end_time": "2025-06-17T16:37:44.319227"}, "test_results": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected", "severity": "CRITICAL", "recommendations": ["If this site should have authentication, verify login form elements are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:34:04.291287", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:34:07.132155", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:34:07.944695", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "step_navigation", "status": "FAILED", "details": "Error clicking Next button on step 1: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "MEDIUM", "recommendations": ["Check Next button functionality on step 1"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:34:38.118902", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_validation", "status": "FAILED", "details": "Form validation test failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "MEDIUM", "recommendations": ["Review form validation implementation"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:08.167238", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "state_management", "status": "FAILED", "details": "State management test failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "MEDIUM", "recommendations": ["Review state management implementation"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:38.563720", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "error_handling", "status": "FAILED", "details": "Validation errors not visible to users", "severity": "LOW", "recommendations": ["Ensure validation errors are properly displayed"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:38.600343", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "complete_journey", "status": "FAILED", "details": "Submit button not found on final step", "severity": "HIGH", "recommendations": ["Ensure submit button is present on final step"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:38.650235", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "critical_workflow_bugs", "status": "PASSED", "details": "No critical workflow bugs detected", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:36:09.021026", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "Request to https://github.com/session missing CSRF token; Request to https://api.github.com/_private/browser/stats missing CSRF token; Request to https://collector.github.com/github/collect missing CSRF token; Request to https://api.github.com/_private/browser/stats missing CSRF token; Request to https://collector.github.com/github/collect missing CSRF token", "severity": "MEDIUM", "recommendations": ["Fix CSRF token header case sensitivity"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:36:12.717549", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 test failed: ElementHandle.fill: Timeout 30000ms exceeded.\nCall log:\n    - fill(\"Test Value\")\n  - attempting fill action\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n      - waiting 100ms\n    59 × waiting for element to be visible, enabled and editable\n       - element is not visible\n     - retrying fill action\n       - waiting 500ms\n; Form 2 test failed: ElementHandle.fill: Timeout 30000ms exceeded.\nCall log:\n    - fill(\"Test Value\")\n  - attempting fill action\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n      - waiting 100ms\n    59 × waiting for element to be visible, enabled and editable\n       - element is not visible\n     - retrying fill action\n       - waiting 500ms\n; Form 3 test failed: ElementHandle.fill: Timeout 30000ms exceeded.\nCall log:\n    - fill(\"Test Value\")\n  - attempting fill action\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n      - waiting 100ms\n    59 × waiting for element to be visible, enabled and editable\n       - element is not visible\n     - retrying fill action\n       - waiting 500ms\n", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1", "Fix form submission issues in form 2", "Fix form submission issues in form 3"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:43.007441", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:44.169501", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "network_requests", "status": "PASSED", "details": "Network requests working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:44.169706", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:44.169769", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "FAILED", "details": "Sensitive data in request body: https://github.com/session", "severity": "HIGH", "recommendations": ["Encrypt or remove sensitive data from API requests"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:44.169927", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T16:37:44.169971", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "FAILED", "details": "40 form inputs without proper labels", "severity": "MEDIUM", "recommendations": ["Associate labels with form inputs"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T16:37:44.317153", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T16:37:44.319219", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected", "severity": "CRITICAL", "recommendations": ["If this site should have authentication, verify login form elements are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:34:04.291287", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "failures": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected", "severity": "CRITICAL", "recommendations": ["If this site should have authentication, verify login form elements are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:34:04.291287", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:34:07.132155", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "step_navigation", "status": "FAILED", "details": "Error clicking Next button on step 1: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "MEDIUM", "recommendations": ["Check Next button functionality on step 1"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:34:38.118902", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_validation", "status": "FAILED", "details": "Form validation test failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "MEDIUM", "recommendations": ["Review form validation implementation"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:08.167238", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "state_management", "status": "FAILED", "details": "State management test failed: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is not visible\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is not visible\n     - retrying click action\n       - waiting 500ms\n", "severity": "MEDIUM", "recommendations": ["Review state management implementation"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:38.563720", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "error_handling", "status": "FAILED", "details": "Validation errors not visible to users", "severity": "LOW", "recommendations": ["Ensure validation errors are properly displayed"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:38.600343", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "complete_journey", "status": "FAILED", "details": "Submit button not found on final step", "severity": "HIGH", "recommendations": ["Ensure submit button is present on final step"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:35:38.650235", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "Request to https://github.com/session missing CSRF token; Request to https://api.github.com/_private/browser/stats missing CSRF token; Request to https://collector.github.com/github/collect missing CSRF token; Request to https://api.github.com/_private/browser/stats missing CSRF token; Request to https://collector.github.com/github/collect missing CSRF token", "severity": "MEDIUM", "recommendations": ["Fix CSRF token header case sensitivity"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:36:12.717549", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 test failed: ElementHandle.fill: Timeout 30000ms exceeded.\nCall log:\n    - fill(\"Test Value\")\n  - attempting fill action\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n      - waiting 100ms\n    59 × waiting for element to be visible, enabled and editable\n       - element is not visible\n     - retrying fill action\n       - waiting 500ms\n; Form 2 test failed: ElementHandle.fill: Timeout 30000ms exceeded.\nCall log:\n    - fill(\"Test Value\")\n  - attempting fill action\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n      - waiting 100ms\n    59 × waiting for element to be visible, enabled and editable\n       - element is not visible\n     - retrying fill action\n       - waiting 500ms\n; Form 3 test failed: ElementHandle.fill: Timeout 30000ms exceeded.\nCall log:\n    - fill(\"Test Value\")\n  - attempting fill action\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and editable\n      - element is not visible\n    - retrying fill action\n      - waiting 100ms\n    59 × waiting for element to be visible, enabled and editable\n       - element is not visible\n     - retrying fill action\n       - waiting 500ms\n", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1", "Fix form submission issues in form 2", "Fix form submission issues in form 3"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:43.007441", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "FAILED", "details": "Sensitive data in request body: https://github.com/session", "severity": "HIGH", "recommendations": ["Encrypt or remove sensitive data from API requests"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:37:44.169927", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "FAILED", "details": "40 form inputs without proper labels", "severity": "MEDIUM", "recommendations": ["Associate labels with form inputs"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T16:37:44.317153", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Fix form submission issues in form 3", "Ensure validation errors are properly displayed", "Fix CSRF token header case sensitivity", "Encrypt or remove sensitive data from API requests", "Security basics look good - consider comprehensive security audit", "Review form validation implementation", "If this site should have authentication, verify login form elements are properly implemented", "Review state management implementation", "Ensure submit button is present on final step", "Check Next button functionality on step 1", "Fix form submission issues in form 2", "Implement proper access control for /profile", "Fix form submission issues in form 1", "Performance looks good - continue monitoring", "Associate labels with form inputs"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 3, "passed": 1, "failed": 2}, "workflow": {"total": 6, "passed": 1, "failed": 5}, "api": {"total": 6, "passed": 3, "failed": 3}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 0, "failed": 1}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"CRITICAL": 1, "HIGH": 4, "LOW": 8, "MEDIUM": 5}, "avg_execution_time": 0.0, "slowest_test": "authentication_flow", "fastest_test": "authentication_flow"}}