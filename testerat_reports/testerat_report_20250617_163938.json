{"metadata": {"report_id": "20250617_163938", "generated_at": "2025-06-17T16:39:38.220063", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "Hacker News", "version": null, "features": ["forms", "data-tables"], "confidence": 0.6, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "Hacker News", "url": "https://news.ycombinator.com"}}, "summary": {"suite_name": "Enhanced Testerat - https://news.ycombinator.com", "total_tests": 11, "passed": 5, "failed": 5, "critical_issues": 1, "success_rate": 45.45454545454545, "total_execution_time": 9.837937116622925, "start_time": "2025-06-17T16:39:38.220373", "end_time": "2025-06-17T16:39:48.058310"}, "test_results": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected", "severity": "CRITICAL", "recommendations": ["If this site should have authentication, verify login form elements are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:39:41.495847", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:39:42.987799", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:39:43.760110", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T16:39:43.803481", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:43.820524", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 missing CSRF token in headers; Form 2 test failed: ElementHandle.get_attribute: Execution context was destroyed, most likely because of a navigation\nCall log:\n  - waiting for locator(\":scope\")\n", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1", "Fix form submission issues in form 2"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:46.162075", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:48.057201", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "network_requests", "status": "PASSED", "details": "Network requests working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:48.057704", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:48.058041", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "FAILED", "details": "Sensitive data in request body: https://news.ycombinator.com/login", "severity": "HIGH", "recommendations": ["Encrypt or remove sensitive data from API requests"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:48.058238", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T16:39:48.058298", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected", "severity": "CRITICAL", "recommendations": ["If this site should have authentication, verify login form elements are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:39:41.495847", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "failures": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected", "severity": "CRITICAL", "recommendations": ["If this site should have authentication, verify login form elements are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:39:41.495847", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T16:39:42.987799", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:43.820524", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 missing CSRF token in headers; Form 2 test failed: ElementHandle.get_attribute: Execution context was destroyed, most likely because of a navigation\nCall log:\n  - waiting for locator(\":scope\")\n", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1", "Fix form submission issues in form 2"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:46.162075", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "FAILED", "details": "Sensitive data in request body: https://news.ycombinator.com/login", "severity": "HIGH", "recommendations": ["Encrypt or remove sensitive data from API requests"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T16:39:48.058238", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement proper access control for /dashboard", "Encrypt or remove sensitive data from API requests", "Implement proper access control for /profile", "Add workflow testing if multi-step processes exist", "Implement CSRF protection for form submissions", "Security basics look good - consider comprehensive security audit", "If this site should have authentication, verify login form elements are properly implemented", "Fix form submission issues in form 1", "Fix form submission issues in form 2"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 3, "passed": 1, "failed": 2}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 6, "passed": 3, "failed": 3}, "security": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"CRITICAL": 1, "HIGH": 3, "LOW": 6, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "authentication_flow", "fastest_test": "authentication_flow"}}