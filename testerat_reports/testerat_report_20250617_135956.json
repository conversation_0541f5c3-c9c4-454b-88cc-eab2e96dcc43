{"metadata": {"report_id": "20250617_135956", "generated_at": "2025-06-17T13:59:56.207957", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.NEXTJS", "app_name": "FAAFO Career Platform - Find Your Path to Career Freedom", "version": null, "features": ["interactive-elements"]}, "app_info": {"name": "FAAFO Career Platform - Find Your Path to Career Freedom", "url": "http://localhost:3000"}}, "summary": {"suite_name": "Enhanced Testerat - http://localhost:3000", "total_tests": 9, "passed": 4, "failed": 4, "critical_issues": 1, "success_rate": 44.44444444444444, "total_execution_time": 11.168195247650146, "start_time": "2025-06-17T13:59:56.208066", "end_time": "2025-06-17T14:00:07.376259"}, "test_results": [{"test_name": "authentication_flow", "status": "FAILED", "details": "Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:59:59.475426", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T14:00:01.242129", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T14:00:04.460433", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T14:00:04.494384", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "CSRF header 'X-CSRF-Token' caused error: 404; CSRF header 'x-csrf-token' caused error: 404; CSRF header 'X-CSRF-TOKEN' caused error: 404; CSRF header 'csrf-token' caused error: 404; CSRF header 'X-Requested-With' caused error: 404; Invalid CSRF token accepted", "severity": "MEDIUM", "recommendations": ["Fix CSRF token header case sensitivity", "Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility", "Improve CSRF token validation"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:04.757902", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": "useCSRFToken.ts:60", "fix_examples": ["Change header from 'x-csrf-token' to 'X-CSRF-Token'"], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 submission not captured", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:07.203857", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:07.360985", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:07.361130", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:07.361187", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [{"test_name": "authentication_flow", "status": "FAILED", "details": "Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:59:59.475426", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "failures": [{"test_name": "authentication_flow", "status": "FAILED", "details": "Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:59:59.475426", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T14:00:01.242129", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "CSRF header 'X-CSRF-Token' caused error: 404; CSRF header 'x-csrf-token' caused error: 404; CSRF header 'X-CSRF-TOKEN' caused error: 404; CSRF header 'csrf-token' caused error: 404; CSRF header 'X-Requested-With' caused error: 404; Invalid CSRF token accepted", "severity": "MEDIUM", "recommendations": ["Fix CSRF token header case sensitivity", "Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility", "Improve CSRF token validation"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:04.757902", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": "useCSRFToken.ts:60", "fix_examples": ["Change header from 'x-csrf-token' to 'X-CSRF-Token'"], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 submission not captured", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T14:00:07.203857", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement proper access control for /profile", "Fix CSRF token header case sensitivity", "Verify authentication success indicators and user state management", "Improve CSRF token validation", "Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility", "Fix form submission issues in form 1", "Add workflow testing if multi-step processes exist"], "fix_examples": ["Change header from 'x-csrf-token' to 'X-CSRF-Token'"], "code_locations": ["useCSRFToken.ts:60"], "statistics": {"categories": {"general": {"total": 3, "passed": 1, "failed": 2}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 5, "passed": 3, "failed": 2}}, "severities": {"CRITICAL": 1, "HIGH": 2, "LOW": 5, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "authentication_flow", "fastest_test": "authentication_flow"}}