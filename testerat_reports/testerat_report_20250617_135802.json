{"metadata": {"report_id": "20250617_135802", "generated_at": "2025-06-17T13:58:02.888391", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.NEXTJS", "app_name": "FAAFO Career Platform - Find Your Path to Career Freedom", "version": null, "features": ["interactive-elements"]}, "app_info": {"name": "FAAFO Career Platform - Find Your Path to Career Freedom", "url": "http://localhost:3000"}}, "summary": {"suite_name": "Enhanced Testerat - http://localhost:3000", "total_tests": 8, "passed": 4, "failed": 3, "critical_issues": 1, "success_rate": 50.0, "total_execution_time": 8.158735275268555, "start_time": "2025-06-17T13:58:02.888611", "end_time": "2025-06-17T13:58:11.047347"}, "test_results": [{"test_name": "authentication_flow", "status": "FAILED", "details": "Custom authentication method detected - manual verification needed; Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Implement custom authentication testing for this application", "Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:58:06.334751", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /profile; Unauthenticated access allowed to protected route: /tools/interview-practice; Unauthenticated access allowed to protected route: /tools/salary-calculator", "severity": "HIGH", "recommendations": ["Implement proper access control for /profile", "Implement proper access control for /tools/interview-practice", "Implement proper access control for /tools/salary-calculator"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:58:09.822912", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:58:10.634608", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T13:58:10.658634", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "CSRF header 'X-CSRF-Token' caused error: 404; CSRF header 'x-csrf-token' caused error: 404; CSRF header 'csrf-token' caused error: 404; Invalid CSRF token accepted", "severity": "MEDIUM", "recommendations": ["Fix CSRF token header case sensitivity", "Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility", "Improve CSRF token validation"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T13:58:10.833792", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": "useCSRFToken.ts:60", "fix_examples": ["Change header from 'x-csrf-token' to 'X-CSRF-Token'"], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T13:58:11.037282", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T13:58:11.037831", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T13:58:11.037880", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [{"test_name": "authentication_flow", "status": "FAILED", "details": "Custom authentication method detected - manual verification needed; Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Implement custom authentication testing for this application", "Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:58:06.334751", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "failures": [{"test_name": "authentication_flow", "status": "FAILED", "details": "Custom authentication method detected - manual verification needed; Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Implement custom authentication testing for this application", "Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:58:06.334751", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /profile; Unauthenticated access allowed to protected route: /tools/interview-practice; Unauthenticated access allowed to protected route: /tools/salary-calculator", "severity": "HIGH", "recommendations": ["Implement proper access control for /profile", "Implement proper access control for /tools/interview-practice", "Implement proper access control for /tools/salary-calculator"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T13:58:09.822912", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "CSRF header 'X-CSRF-Token' caused error: 404; CSRF header 'x-csrf-token' caused error: 404; CSRF header 'csrf-token' caused error: 404; Invalid CSRF token accepted", "severity": "MEDIUM", "recommendations": ["Fix CSRF token header case sensitivity", "Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility", "Improve CSRF token validation"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T13:58:10.833792", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": "useCSRFToken.ts:60", "fix_examples": ["Change header from 'x-csrf-token' to 'X-CSRF-Token'"], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Add workflow testing if multi-step processes exist", "Change 'x-csrf-token' to 'X-CSRF-Token' for better compatibility", "Implement custom authentication testing for this application", "Implement proper access control for /tools/interview-practice", "Improve CSRF token validation", "Implement proper access control for /tools/salary-calculator", "Fix CSRF token header case sensitivity", "Verify authentication success indicators and user state management", "Implement proper access control for /profile"], "fix_examples": ["Change header from 'x-csrf-token' to 'X-CSRF-Token'"], "code_locations": ["useCSRFToken.ts:60"], "statistics": {"categories": {"general": {"total": 3, "passed": 1, "failed": 2}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 3, "failed": 1}}, "severities": {"CRITICAL": 1, "HIGH": 1, "LOW": 5, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "authentication_flow", "fastest_test": "authentication_flow"}}