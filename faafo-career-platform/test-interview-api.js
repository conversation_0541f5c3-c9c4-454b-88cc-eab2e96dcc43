const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Mock session for testing
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>'
  }
};

// Test configuration
const testConfig = {
  sessionType: 'QUICK_PRACTICE',
  experienceLevel: 'INTERMEDIATE',
  difficulty: 'INTERMEDIATE',
  totalQuestions: 5,
  careerPath: 'Software Engineering',
  companyType: 'Tech Startup',
  industryFocus: 'Technology',
  specificRole: 'Full Stack Developer',
  interviewType: 'VIDEO',
  preparationTime: '15 minutes',
  focusAreas: ['Problem Solving', 'Technical Skills']
};

async function testInterviewPracticeAPI() {
  try {
    console.log('🧪 Testing Interview Practice API Endpoints...\n');

    // Get test user
    const testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      console.error('❌ Test user not found. Please create a test user first.');
      return;
    }

    console.log(`✅ Found test user: ${testUser.email}`);

    // Test 1: POST /api/interview-practice - Create Session
    console.log('\n1. Testing POST /api/interview-practice (Create Session)...');
    
    const createSessionData = {
      ...testConfig,
      userId: testUser.id
    };

    const newSession = await prisma.interviewSession.create({
      data: createSessionData,
      include: {
        questions: true,
        responses: true
      }
    });

    console.log(`   ✅ Session created: ${newSession.id}`);
    console.log(`   ✅ Session type: ${newSession.sessionType}`);
    console.log(`   ✅ Status: ${newSession.status}`);

    // Test 2: GET /api/interview-practice - List Sessions
    console.log('\n2. Testing GET /api/interview-practice (List Sessions)...');
    
    const userSessions = await prisma.interviewSession.findMany({
      where: { userId: testUser.id },
      include: {
        questions: {
          select: {
            id: true,
            questionType: true,
            category: true,
            difficulty: true,
            questionOrder: true,
          },
          orderBy: { questionOrder: 'asc' }
        },
        responses: {
          select: {
            id: true,
            questionId: true,
            isCompleted: true,
            aiScore: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`   ✅ Found ${userSessions.length} sessions for user`);
    console.log(`   ✅ Latest session: ${userSessions[0]?.id}`);

    // Test 3: GET /api/interview-practice/[sessionId] - Get Session Details
    console.log('\n3. Testing GET /api/interview-practice/[sessionId] (Get Session Details)...');
    
    const sessionDetails = await prisma.interviewSession.findFirst({
      where: {
        id: newSession.id,
        userId: testUser.id,
      },
      include: {
        questions: {
          include: {
            responses: {
              where: { userId: testUser.id },
              select: {
                id: true,
                responseText: true,
                audioUrl: true,
                responseTime: true,
                preparationTime: true,
                aiScore: true,
                aiAnalysis: true,
                feedback: true,
                isCompleted: true,
                userNotes: true,
                createdAt: true,
                updatedAt: true,
              },
            },
          },
          orderBy: { questionOrder: 'asc' },
        },
        responses: {
          where: { userId: testUser.id },
          select: {
            id: true,
            questionId: true,
            isCompleted: true,
            aiScore: true,
            responseTime: true,
          },
        },
      },
    });

    console.log(`   ✅ Session details retrieved: ${sessionDetails.id}`);
    console.log(`   ✅ Questions count: ${sessionDetails.questions.length}`);
    console.log(`   ✅ Responses count: ${sessionDetails.responses.length}`);

    // Test 4: POST /api/interview-practice/[sessionId]/questions - Generate Questions
    console.log('\n4. Testing POST /api/interview-practice/[sessionId]/questions (Generate Questions)...');
    
    // Create test questions for the session
    const testQuestions = [
      {
        sessionId: newSession.id,
        questionText: "Tell me about yourself and your background.",
        questionType: 'BEHAVIORAL',
        category: 'GENERAL',
        difficulty: 'BEGINNER',
        expectedDuration: 180,
        context: "This is a common opening question to assess communication skills.",
        hints: ["Use STAR method", "Keep it professional", "Focus on relevant experience"],
        questionOrder: 1,
        isRequired: true,
        tags: ["introduction", "background"]
      },
      {
        sessionId: newSession.id,
        questionText: "Describe a challenging project you worked on.",
        questionType: 'BEHAVIORAL',
        category: 'PROBLEM_SOLVING',
        difficulty: 'INTERMEDIATE',
        expectedDuration: 240,
        context: "Assesses problem-solving and project management skills.",
        hints: ["Describe the challenge", "Explain your approach", "Highlight the outcome"],
        questionOrder: 2,
        isRequired: true,
        tags: ["projects", "challenges"]
      }
    ];

    const createdQuestions = await prisma.interviewQuestion.createMany({
      data: testQuestions
    });

    console.log(`   ✅ Created ${createdQuestions.count} test questions`);

    // Update session with question count
    await prisma.interviewSession.update({
      where: { id: newSession.id },
      data: { totalQuestions: testQuestions.length }
    });

    // Test 5: GET /api/interview-practice/[sessionId]/questions - Get Questions
    console.log('\n5. Testing GET /api/interview-practice/[sessionId]/questions (Get Questions)...');
    
    const sessionQuestions = await prisma.interviewQuestion.findMany({
      where: { sessionId: newSession.id },
      orderBy: { questionOrder: 'asc' }
    });

    console.log(`   ✅ Retrieved ${sessionQuestions.length} questions`);
    console.log(`   ✅ First question: "${sessionQuestions[0]?.questionText.substring(0, 50)}..."`);

    // Test 6: POST /api/interview-practice/[sessionId]/responses - Submit Response
    console.log('\n6. Testing POST /api/interview-practice/[sessionId]/responses (Submit Response)...');
    
    const testResponse = {
      userId: testUser.id,
      sessionId: newSession.id,
      questionId: sessionQuestions[0].id,
      responseText: "I am a passionate software developer with 3 years of experience in full-stack development. I have worked on various projects using React, Node.js, and PostgreSQL. I enjoy solving complex problems and learning new technologies.",
      responseTime: 120,
      preparationTime: 30,
      isCompleted: true,
      userNotes: "Felt confident about this response"
    };

    const createdResponse = await prisma.interviewResponse.create({
      data: testResponse,
      include: {
        question: true
      }
    });

    console.log(`   ✅ Response submitted: ${createdResponse.id}`);
    console.log(`   ✅ Response length: ${createdResponse.responseText.length} characters`);

    // Test 7: GET /api/interview-practice/[sessionId]/responses - Get Responses
    console.log('\n7. Testing GET /api/interview-practice/[sessionId]/responses (Get Responses)...');
    
    const sessionResponses = await prisma.interviewResponse.findMany({
      where: {
        sessionId: newSession.id,
        userId: testUser.id,
      },
      include: {
        question: {
          select: {
            id: true,
            questionText: true,
            questionType: true,
            category: true,
            difficulty: true,
            expectedDuration: true,
            context: true,
            hints: true,
            questionOrder: true,
          },
        },
      },
      orderBy: {
        question: {
          questionOrder: 'asc',
        },
      },
    });

    console.log(`   ✅ Retrieved ${sessionResponses.length} responses`);
    console.log(`   ✅ First response completed: ${sessionResponses[0]?.isCompleted}`);

    // Test 8: PATCH /api/interview-practice/[sessionId] - Update Session
    console.log('\n8. Testing PATCH /api/interview-practice/[sessionId] (Update Session)...');
    
    const updatedSession = await prisma.interviewSession.update({
      where: { id: newSession.id },
      data: {
        status: 'COMPLETED',
        completedQuestions: 1,
        overallScore: 8.5,
        timeSpent: 15,
        completedAt: new Date()
      }
    });

    console.log(`   ✅ Session updated: ${updatedSession.status}`);
    console.log(`   ✅ Overall score: ${updatedSession.overallScore}`);

    // Test 9: GET /api/interview-practice/progress - Get Progress
    console.log('\n9. Testing GET /api/interview-practice/progress (Get Progress)...');
    
    // Create or update progress record
    const progressData = {
      userId: testUser.id,
      skillArea: 'GENERAL',
      competencyLevel: 'INTERMEDIATE',
      totalSessions: 1,
      completedSessions: 1,
      averageScore: 8.5,
      bestScore: 8.5,
      lastSessionScore: 8.5,
      totalPracticeTime: 15,
      lastPracticed: new Date(),
      improvementRate: 0.1,
      streakCount: 1,
      longestStreak: 1,
      lastStreakDate: new Date()
    };

    const progressRecord = await prisma.interviewProgress.upsert({
      where: {
        userId_skillArea: {
          userId: testUser.id,
          skillArea: 'GENERAL'
        }
      },
      update: progressData,
      create: progressData
    });

    console.log(`   ✅ Progress record created/updated: ${progressRecord.id}`);
    console.log(`   ✅ Average score: ${progressRecord.averageScore}`);

    // Clean up test data
    console.log('\n10. Cleaning up test data...');
    
    await prisma.interviewResponse.deleteMany({
      where: { sessionId: newSession.id }
    });
    
    await prisma.interviewQuestion.deleteMany({
      where: { sessionId: newSession.id }
    });
    
    await prisma.interviewSession.delete({
      where: { id: newSession.id }
    });

    await prisma.interviewProgress.delete({
      where: { id: progressRecord.id }
    });

    console.log('   ✅ Test data cleaned up');

    console.log('\n🎉 All Interview Practice API endpoints tested successfully!');
    
  } catch (error) {
    console.error('❌ API testing failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testInterviewPracticeAPI();
