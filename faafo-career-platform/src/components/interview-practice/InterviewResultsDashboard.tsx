'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  Star,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Calendar,
  Award,
  Users,
  BookOpen,
  Filter,
  Download,
  Eye,
  Play
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, <PERSON>, Pie<PERSON>hart, Pie, Cell } from 'recharts';

interface InterviewSession {
  id: string;
  sessionType: string;
  status: string;
  totalQuestions: number;
  completedQuestions: number;
  overallScore?: number;
  timeSpent: number;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  createdAt: string;
  completedAt?: string;
}

interface ProgressData {
  overallProgress: {
    totalSessions: number;
    completedSessions: number;
    totalQuestions: number;
    completedQuestions: number;
    averageScore: number | null;
    bestScore: number | null;
    totalPracticeTime: number;
    improvementRate: number | null;
    currentStreak: number;
    longestStreak: number;
    completionRate: number;
  };
  skillAreaProgress: Array<{
    skillArea: string;
    totalSessions: number;
    completedSessions: number;
    averageScore: number | null;
    bestScore: number | null;
    lastSessionScore: number | null;
    totalPracticeTime: number;
    lastPracticed: string;
  }>;
  questionTypeStats: Array<{
    questionType: string;
    category: string;
    responseCount: number;
    averageScore: number;
    averageResponseTime: number;
  }>;
  recentSessions: InterviewSession[];
  improvementTrend: number[];
}

interface InterviewResultsDashboardProps {
  onViewSession: (sessionId: string) => void;
  onStartNewSession: () => void;
}

export default function InterviewResultsDashboard({
  onViewSession,
  onStartNewSession,
}: InterviewResultsDashboardProps) {
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('all');
  const [sessionTypeFilter, setSessionTypeFilter] = useState('all');

  useEffect(() => {
    fetchProgressData();
  }, [timeFilter, sessionTypeFilter]);

  const fetchProgressData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (timeFilter !== 'all') params.append('timeFilter', timeFilter);
      if (sessionTypeFilter !== 'all') params.append('sessionType', sessionTypeFilter);

      const response = await fetch(`/api/interview-practice/progress?${params}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setProgressData(result.data);
        } else {
          console.error('API returned error:', result.error);
        }
      } else {
        console.error('Failed to fetch progress data:', response.status);
      }
    } catch (error) {
      console.error('Failed to fetch progress data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 dark:text-green-400';
    if (score >= 6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Completed</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'abandoned':
        return <Badge variant="destructive">Abandoned</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const pieChartColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!progressData) {
    return (
      <div className="w-full max-w-6xl mx-auto">
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Data Available</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Unable to load your interview practice progress.
            </p>
            <Button onClick={fetchProgressData}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Interview Practice Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track your progress and improve your interview skills
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeFilter} onValueChange={setTimeFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sessionTypeFilter} onValueChange={setSessionTypeFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="quick_practice">Quick Practice</SelectItem>
              <SelectItem value="focused_session">Focused Session</SelectItem>
              <SelectItem value="mock_interview">Mock Interview</SelectItem>
              <SelectItem value="behavioral_practice">Behavioral</SelectItem>
              <SelectItem value="technical_practice">Technical</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={onStartNewSession}>
            <Play className="h-4 w-4 mr-2" />
            New Session
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Sessions</p>
                <p className="text-2xl font-bold">{progressData.overallProgress.totalSessions}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {progressData.overallProgress.completedSessions} completed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Average Score</p>
                <p className={`text-2xl font-bold ${progressData.overallProgress.averageScore ? getScoreColor(progressData.overallProgress.averageScore) : 'text-gray-400'}`}>
                  {progressData.overallProgress.averageScore ? `${progressData.overallProgress.averageScore.toFixed(1)}/10` : 'N/A'}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Best: {progressData.overallProgress.bestScore ? `${progressData.overallProgress.bestScore.toFixed(1)}/10` : 'N/A'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Practice Time</p>
                <p className="text-2xl font-bold">
                  {formatDuration(progressData.overallProgress.totalPracticeTime)}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {progressData.overallProgress.completionRate.toFixed(1)}% completion rate
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Streak</p>
                <p className="text-2xl font-bold">{progressData.overallProgress.currentStreak}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Longest: {progressData.overallProgress.longestStreak} days
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Progress Over Time */}
        <Card>
          <CardHeader>
            <CardTitle>Progress Over Time</CardTitle>
            <CardDescription>Your improvement trend over recent sessions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              {progressData.improvementTrend.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={progressData.improvementTrend.map((score, index) => ({
                    session: `Session ${index + 1}`,
                    score: score
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="session" />
                    <YAxis domain={[0, 10]} />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="score"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      name="Average Score"
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No trend data available</p>
                    <p className="text-sm">Complete more sessions to see your progress</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Question Type Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Performance by Question Type</CardTitle>
            <CardDescription>Your strengths and areas for improvement</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {progressData.questionTypeStats.length > 0 ? (
                progressData.questionTypeStats.map((stat, index) => {
                  const percentage = (stat.averageScore / 10) * 100;
                  return (
                    <div key={`${stat.questionType}-${stat.category}-${index}`} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-sm font-medium">
                            {stat.questionType.replace('_', ' ')}
                          </span>
                          <p className="text-xs text-gray-500">
                            {stat.category.replace('_', ' ')} • {stat.responseCount} responses
                          </p>
                        </div>
                        <span className={`text-sm font-bold ${getScoreColor(stat.averageScore)}`}>
                          {stat.averageScore.toFixed(1)}/10
                        </span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Target className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No performance data available</p>
                  <p className="text-sm">Complete practice sessions to see your performance breakdown</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Skill Area Progress */}
      {progressData.skillAreaProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Skill Area Progress</CardTitle>
            <CardDescription>Detailed breakdown of your performance by skill area</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {progressData.skillAreaProgress.map((skill) => (
                <Card key={skill.skillArea} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm">
                          {skill.skillArea.replace('_', ' ')}
                        </h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Last practiced: {new Date(skill.lastPracticed).toLocaleDateString()}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">Sessions</p>
                          <p className="font-medium">{skill.completedSessions}/{skill.totalSessions}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">Practice Time</p>
                          <p className="font-medium">{formatDuration(skill.totalPracticeTime)}</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 dark:text-gray-400">Average Score</span>
                          <span className={`text-sm font-bold ${skill.averageScore ? getScoreColor(skill.averageScore) : 'text-gray-400'}`}>
                            {skill.averageScore ? `${skill.averageScore.toFixed(1)}/10` : 'N/A'}
                          </span>
                        </div>
                        {skill.averageScore && (
                          <Progress value={(skill.averageScore / 10) * 100} className="h-1" />
                        )}
                      </div>

                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600 dark:text-gray-400">Best Score</span>
                        <span className={`font-medium ${skill.bestScore ? getScoreColor(skill.bestScore) : 'text-gray-400'}`}>
                          {skill.bestScore ? `${skill.bestScore.toFixed(1)}/10` : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Insights */}
      {progressData.overallProgress.totalSessions > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
            <CardDescription>AI-powered recommendations based on your practice data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Improvement Trend */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                  Improvement Trend
                </h4>
                {progressData.overallProgress.improvementRate !== null ? (
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      {progressData.overallProgress.improvementRate > 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`font-medium ${progressData.overallProgress.improvementRate > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {progressData.overallProgress.improvementRate > 0 ? '+' : ''}{progressData.overallProgress.improvementRate.toFixed(1)}%
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {progressData.overallProgress.improvementRate > 0
                        ? 'Your scores are improving! Keep up the great work.'
                        : 'Focus on consistent practice to improve your scores.'
                      }
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Complete more sessions to see your improvement trend.
                  </p>
                )}
              </div>

              {/* Recommendations */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center">
                  <Target className="h-4 w-4 mr-2 text-blue-500" />
                  Recommendations
                </h4>
                <div className="space-y-2">
                  {progressData.overallProgress.currentStreak === 0 && (
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                      <p className="text-sm text-yellow-800 dark:text-yellow-200">
                        Start a practice streak! Consistent daily practice leads to better results.
                      </p>
                    </div>
                  )}

                  {progressData.overallProgress.completionRate < 80 && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        Try to complete more questions in each session for better practice value.
                      </p>
                    </div>
                  )}

                  {progressData.overallProgress.averageScore && progressData.overallProgress.averageScore < 6 && (
                    <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                      <p className="text-sm text-purple-800 dark:text-purple-200">
                        Focus on understanding question patterns and practicing the STAR method.
                      </p>
                    </div>
                  )}

                  {progressData.questionTypeStats.length > 0 && (
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                      <p className="text-sm text-green-800 dark:text-green-200">
                        Your strongest area: {progressData.questionTypeStats[0]?.questionType.replace('_', ' ')}
                        ({progressData.questionTypeStats[0]?.averageScore.toFixed(1)}/10)
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Sessions</CardTitle>
              <CardDescription>Your latest interview practice sessions</CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {progressData.recentSessions.map((session) => (
              <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <BookOpen className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-medium">
                      {session.sessionType.replace('_', ' ')}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(session.createdAt).toLocaleDateString()} • {formatDuration(session.timeSpent)}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      {getStatusBadge(session.status)}
                      {session.careerPath && (
                        <Badge variant="outline" className="text-xs">
                          {session.careerPath}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {session.completedQuestions}/{session.totalQuestions} questions
                    </p>
                    {session.overallScore && (
                      <p className={`text-sm font-bold ${getScoreColor(session.overallScore)}`}>
                        {session.overallScore.toFixed(1)}/10
                      </p>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewSession(session.id)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {progressData.recentSessions.length === 0 && (
            <div className="text-center py-8">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Sessions Yet</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Start your first interview practice session to see your progress here.
              </p>
              <Button onClick={onStartNewSession}>
                <Play className="h-4 w-4 mr-2" />
                Start First Session
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}