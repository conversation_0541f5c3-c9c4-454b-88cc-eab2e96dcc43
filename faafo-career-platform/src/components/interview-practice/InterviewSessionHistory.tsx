'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar,
  Clock, 
  Target, 
  Star, 
  Search,
  Filter,
  Download,
  Eye,
  TrendingUp,
  TrendingDown,
  BarChart3
} from 'lucide-react';

interface InterviewSession {
  id: string;
  sessionType: string;
  status: string;
  totalQuestions: number;
  completedQuestions: number;
  overallScore?: number;
  timeSpent: number;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  createdAt: string;
  completedAt?: string;
  lastActiveAt: string;
}

interface InterviewSessionHistoryProps {
  onViewSession: (sessionId: string) => void;
  onStartNewSession: () => void;
}

export default function InterviewSessionHistory({
  onViewSession,
  onStartNewSession,
}: InterviewSessionHistoryProps) {
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    fetchSessions();
  }, [statusFilter, typeFilter, sortBy, sortOrder]);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (typeFilter !== 'all') params.append('sessionType', typeFilter);
      params.append('limit', '50'); // Get more sessions for history
      params.append('sortBy', sortBy);
      params.append('sortOrder', sortOrder);

      const response = await fetch(`/api/interview-practice?${params}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSessions(result.data.sessions || []);
        }
      }
    } catch (error) {
      console.error('Failed to fetch sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = searchTerm === '' || 
      session.sessionType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.careerPath?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.companyType?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 dark:text-green-400';
    if (score >= 6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Completed</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'paused':
        return <Badge variant="outline">Paused</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSessionTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'quick_practice':
        return <Clock className="h-4 w-4" />;
      case 'mock_interview':
        return <Target className="h-4 w-4" />;
      case 'behavioral_practice':
        return <Star className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Session History</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Complete history of your interview practice sessions
          </p>
        </div>
        <Button onClick={onStartNewSession}>
          <Target className="h-4 w-4 mr-2" />
          New Session
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search sessions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="quick_practice">Quick Practice</SelectItem>
                <SelectItem value="focused_session">Focused Session</SelectItem>
                <SelectItem value="mock_interview">Mock Interview</SelectItem>
                <SelectItem value="behavioral_practice">Behavioral</SelectItem>
                <SelectItem value="technical_practice">Technical</SelectItem>
              </SelectContent>
            </Select>
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">Newest First</SelectItem>
                <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                <SelectItem value="overallScore-desc">Highest Score</SelectItem>
                <SelectItem value="overallScore-asc">Lowest Score</SelectItem>
                <SelectItem value="timeSpent-desc">Longest Time</SelectItem>
                <SelectItem value="timeSpent-asc">Shortest Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Sessions List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Practice Sessions</CardTitle>
              <CardDescription>
                {filteredSessions.length} session{filteredSessions.length !== 1 ? 's' : ''} found
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {filteredSessions.length > 0 ? (
            <div className="space-y-4">
              {filteredSessions.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                      {getSessionTypeIcon(session.sessionType)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">
                          {session.sessionType.replace('_', ' ')}
                        </h4>
                        {getStatusBadge(session.status)}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {formatDate(session.createdAt)} • {formatDuration(session.timeSpent)}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        {session.careerPath && (
                          <Badge variant="outline" className="text-xs">
                            {session.careerPath}
                          </Badge>
                        )}
                        {session.experienceLevel && (
                          <Badge variant="outline" className="text-xs">
                            {session.experienceLevel}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {session.completedQuestions}/{session.totalQuestions} questions
                      </p>
                      {session.overallScore && (
                        <p className={`text-sm font-bold ${getScoreColor(session.overallScore)}`}>
                          {session.overallScore.toFixed(1)}/10
                        </p>
                      )}
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onViewSession(session.id)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Sessions Found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'Try adjusting your filters to see more sessions.'
                  : 'Start your first interview practice session to see your history here.'
                }
              </p>
              <Button onClick={onStartNewSession}>
                <Target className="h-4 w-4 mr-2" />
                Start First Session
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
