'use client';

import React from 'react';
import { Breadcrumb } from './Breadcrumb';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
}

interface PageLayoutProps {
  children: React.ReactNode;
  showBreadcrumbs?: boolean;
  customBreadcrumbs?: BreadcrumbItem[];
  breadcrumbClassName?: string;
  containerClassName?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' | '7xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md', 
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '4xl': 'max-w-4xl',
  '6xl': 'max-w-6xl',
  '7xl': 'max-w-7xl',
  full: 'max-w-full'
};

const paddingClasses = {
  none: '',
  sm: 'px-4 py-4',
  md: 'px-4 sm:px-6 py-6',
  lg: 'px-4 sm:px-6 lg:px-8 py-8'
};

export function PageLayout({ 
  children, 
  showBreadcrumbs = true,
  customBreadcrumbs,
  breadcrumbClassName = '',
  containerClassName = '',
  maxWidth = '7xl',
  padding = 'md'
}: PageLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {showBreadcrumbs && (
        <div className={`border-b border-border bg-gray-50 dark:bg-gray-900/50 ${maxWidthClasses[maxWidth]} mx-auto`}>
          <div className={`${paddingClasses.sm} ${breadcrumbClassName}`}>
            <Breadcrumb customItems={customBreadcrumbs} />
          </div>
        </div>
      )}
      
      <div className={`${maxWidthClasses[maxWidth]} mx-auto ${paddingClasses[padding]} ${containerClassName}`}>
        {children}
      </div>
    </div>
  );
}

export default PageLayout;
