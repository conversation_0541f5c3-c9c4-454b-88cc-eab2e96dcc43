import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth';

// SECURITY FIX: Enhanced CSRF token storage with cleanup
// TODO: Replace with Redis or database in production
const csrfTokens = new Map<string, { token: string; expires: number; created: number }>();

// Cleanup expired tokens every 5 minutes to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  Array.from(csrfTokens.entries()).forEach(([key, value]) => {
    if (now > value.expires) {
      csrfTokens.delete(key);
    }
  });
}, 5 * 60 * 1000);

export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export async function getCSRFToken(request: NextRequest): Promise<string> {
  const session = await getServerSession(authOptions);
  // SECURITY FIX: Use more specific user identification to prevent token sharing
  const sessionId = session?.user?.id || `anon_${request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'}`;
  
  // Check if we have a valid token
  const existing = csrfTokens.get(sessionId);
  if (existing && existing.expires > Date.now()) {
    return existing.token;
  }
  
  // Generate new token
  const token = generateCSRFToken();
  const expires = Date.now() + (60 * 60 * 1000); // 1 hour
  
  csrfTokens.set(sessionId, { token, expires, created: Date.now() });
  
  // Clean up expired tokens
  Array.from(csrfTokens.entries()).forEach(([key, value]) => {
    if (value.expires <= Date.now()) {
      csrfTokens.delete(key);
    }
  });
  
  return token;
}

export async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {
  const session = await getServerSession(authOptions);
  // SECURITY FIX: Use same identification logic as token generation
  const sessionId = session?.user?.id || `anon_${request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'}`;
  
  const stored = csrfTokens.get(sessionId);
  if (!stored || stored.expires <= Date.now()) {
    return false;
  }
  
  return stored.token === token;
}

export async function withCSRFProtection(
  request: NextRequest,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  // Skip CSRF for GET requests
  if (request.method === 'GET') {
    return handler();
  }
  
  // Get CSRF token from header or body
  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('csrf-token');
  
  if (!csrfToken) {
    return NextResponse.json(
      { error: 'CSRF token missing' },
      { status: 403 }
    );
  }
  
  const isValid = await validateCSRFToken(request, csrfToken);
  if (!isValid) {
    return NextResponse.json(
      { error: 'Invalid CSRF token' },
      { status: 403 }
    );
  }
  
  return handler();
}

// API endpoint to get CSRF token
export async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {
  const token = await getCSRFToken(request);
  return NextResponse.json({ csrfToken: token });
}
