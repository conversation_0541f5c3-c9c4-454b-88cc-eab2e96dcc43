import { PrismaClient } from "@prisma/client";

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Build optimized database URL with connection pool parameters
function buildDatabaseUrl(): string {
  const baseUrl = process.env.POSTGRES_PRISMA_URL || process.env.DATABASE_URL;

  if (!baseUrl) {
    throw new Error('DATABASE_URL or POSTGRES_PRISMA_URL must be defined');
  }

  // If URL already has parameters, don't modify it
  if (baseUrl.includes('?')) {
    return baseUrl;
  }

  // Add connection pool parameters for PostgreSQL
  const poolParams = new URLSearchParams({
    'connection_limit': '10',
    'pool_timeout': '20',
    'connect_timeout': '20',
    'socket_timeout': '20',
    'statement_timeout': '30000',
    'idle_timeout': '300',
    'pgbouncer': 'true'
  });

  return `${baseUrl}?${poolParams.toString()}`;
}

const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: buildDatabaseUrl(),
    },
  },
  // Optimize transaction settings
  transactionOptions: {
    maxWait: 5000, // 5 seconds
    timeout: 10000, // 10 seconds
  },
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Graceful shutdown handler - moved to process level as recommended for Prisma 5.0+
process.on('beforeExit', async () => {
  console.log('Process exiting, disconnecting Prisma client...');
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  console.log('Received SIGINT, disconnecting Prisma client...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, disconnecting Prisma client...');
  await prisma.$disconnect();
  process.exit(0);
});

export { prisma };
export default prisma;