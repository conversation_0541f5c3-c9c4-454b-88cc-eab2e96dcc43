import { NextAuthOptions, User as <PERSON><PERSON><PERSON><PERSON><PERSON>, Session } from "next-auth";
import { JWT } from "next-auth/jwt";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import Email<PERSON>rovider from "next-auth/providers/email";
import { PrismaAdapter } from "@auth/prisma-adapter";
import bcrypt from "bcryptjs";
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { VerificationEmail } from '@/emails/VerificationEmail';

// Augment the NextAuthUser type to include id
interface User extends NextAuthUser {
  id: string;
}

// Augment the Session.user type
interface ExtendedSession extends Session {
  user?: User & {
    id: string;
  };
}

// Augment the JWT type to include id, email, and name
interface ExtendedJWT extends JWT {
  id: string;
  email: string;
  name: string;
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<User | null> {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user) {
          return null;
        }

        // Check if account is locked
        if (user.lockedUntil && user.lockedUntil > new Date()) {
          throw new Error('Account is temporarily locked due to too many failed login attempts. Please try again later.');
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password);

        if (!isPasswordValid) {
          // Increment failed login attempts
          const failedAttempts = user.failedLoginAttempts + 1;
          const maxAttempts = 5;
          const lockoutDuration = 15 * 60 * 1000; // 15 minutes in milliseconds

          if (failedAttempts >= maxAttempts) {
            // Lock the account
            await prisma.user.update({
              where: { id: user.id },
              data: {
                failedLoginAttempts: failedAttempts,
                lockedUntil: new Date(Date.now() + lockoutDuration)
              }
            });
            throw new Error('Account locked due to too many failed login attempts. Please try again in 15 minutes.');
          } else {
            // Update failed attempts count
            await prisma.user.update({
              where: { id: user.id },
              data: {
                failedLoginAttempts: failedAttempts
              }
            });
          }
          return null;
        }

        // Check if email is verified (bypass in development)
        if (!user.emailVerified && process.env.NODE_ENV === 'production') {
          throw new Error('Please verify your email address before signing in. Check your inbox for a verification link.');
        }

        // Reset failed login attempts on successful login
        if (user.failedLoginAttempts > 0 || user.lockedUntil) {
          await prisma.user.update({
            where: { id: user.id },
            data: {
              failedLoginAttempts: 0,
              lockedUntil: null
            }
          });
        }

        return { id: user.id, email: user.email, name: user.name } as User;
      }
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: parseInt(process.env.EMAIL_SERVER_PORT || "587"), // Default to 587 if not set
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM || '<EMAIL>',
      sendVerificationRequest: async ({ identifier: email, url }) => {
        try {
          const user = await prisma.user.findUnique({
            where: { email },
          });
          if (user) {
            await sendEmail({
              to: email,
              subject: "Verify your email for FAAFO Career Platform",
              template: <VerificationEmail username={user.name || email} verificationLink={url} />,
            });
            console.log(`Verification email sent to ${email}`);
          } else {
            console.warn(`Attempted to send verification email to non-existent user via magic link: ${email}`);
          }
        } catch (error) {
          console.error(`Failed to send verification email to ${email}:`, error);
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 hours (reduced for security)
    updateAge: 30 * 60, // 30 minutes - regenerate session more frequently
  },
  jwt: {
    maxAge: 8 * 60 * 60, // 8 hours (reduced for security)
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'strict',
        path: '/',
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        maxAge: 8 * 60 * 60, // 8 hours
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        httpOnly: true,
        sameSite: 'strict',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'strict',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  callbacks: {
    async jwt({ token, user, trigger }): Promise<ExtendedJWT> {
      const now = Math.floor(Date.now() / 1000);

      // Regenerate session ID on login for security
      if (trigger === 'signIn' || trigger === 'signUp') {
        token.sessionId = crypto.randomUUID();
        token.iat = now;
        token.lastActivity = now;
      }

      // Check for session timeout (force re-authentication after 8 hours)
      if (token.iat && typeof token.iat === 'number' && (now - token.iat) > (8 * 60 * 60)) {
        throw new Error('Session expired');
      }

      // Regenerate session ID periodically for security (every 30 minutes)
      if (token.lastActivity && typeof token.lastActivity === 'number' && (now - token.lastActivity) > (30 * 60)) {
        token.sessionId = crypto.randomUUID();
        token.lastActivity = now;
      }

      if (user) {
        token.id = (user as User).id;
        token.email = (user as User).email;
        token.name = (user as User).name;
      }

      // Update last activity timestamp
      token.lastActivity = now;

      return token as ExtendedJWT;
    },
    async session({ session, token }): Promise<ExtendedSession> {
      if (session.user && token.id) {
        (session.user as User).id = token.id as string;
        if (token.email) {
          (session.user as User).email = token.email as string;
        }
        if (token.name) {
          (session.user as User).name = token.name as string;
        }
      }

      // Add session security metadata
      (session as any).sessionId = token.sessionId;
      (session as any).lastActivity = token.lastActivity;

      return session as ExtendedSession;
    },
  },
  events: {
    async createUser(message) {
      console.log("User created:", message.user.email);
    },
  },
  pages: {
    signIn: '/login',
  }
}; 