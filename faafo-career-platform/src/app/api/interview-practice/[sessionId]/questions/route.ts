import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { geminiService } from '@/lib/services/geminiService';
import { withError<PERSON>andler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Fallback questions when AI service fails
function generateFallbackQuestions(count: number, difficulty: string = 'INTERMEDIATE') {
  const fallbackQuestions = [
    {
      questionText: "Tell me about yourself and your professional background.",
      questionType: "BEHAVIORAL",
      category: "GENERAL",
      difficulty: "BEGINNER",
      expectedDuration: 180,
      context: "This is a common opening question to assess communication skills and professional summary.",
      hints: {
        structure: "Use a brief professional summary highlighting relevant experience",
        keyPoints: ["Current role", "Key achievements", "Career goals"],
        commonMistakes: ["Being too personal", "Rambling without structure"]
      },
      followUpQuestions: ["What interests you about this role?"],
      industrySpecific: false,
      tags: ["introduction", "general"],
      isRequired: true
    },
    {
      questionText: "Why are you interested in this position?",
      questionType: "BEHAVIORAL",
      category: "GENERAL",
      difficulty: "BEGINNER",
      expectedDuration: 120,
      context: "Assesses motivation and research about the role and company.",
      hints: {
        structure: "Connect your skills and interests to the role requirements",
        keyPoints: ["Role alignment", "Company research", "Career goals"],
        commonMistakes: ["Generic answers", "Focusing only on benefits to you"]
      },
      followUpQuestions: ["What do you know about our company?"],
      industrySpecific: false,
      tags: ["motivation", "research"],
      isRequired: true
    },
    {
      questionText: "Describe a challenging situation you faced at work and how you handled it.",
      questionType: "BEHAVIORAL",
      category: "PROBLEM_SOLVING",
      difficulty: "INTERMEDIATE",
      expectedDuration: 240,
      context: "Uses STAR method to assess problem-solving and resilience.",
      hints: {
        structure: "Use STAR method: Situation, Task, Action, Result",
        keyPoints: ["Clear problem description", "Your specific actions", "Positive outcome"],
        commonMistakes: ["Vague situations", "Not taking ownership", "No clear result"]
      },
      followUpQuestions: ["What would you do differently?", "How did this experience change your approach?"],
      industrySpecific: false,
      tags: ["problem-solving", "star-method"],
      isRequired: true
    },
    {
      questionText: "What are your greatest strengths and how do they apply to this role?",
      questionType: "BEHAVIORAL",
      category: "SOFT_SKILLS",
      difficulty: "BEGINNER",
      expectedDuration: 150,
      context: "Evaluates self-awareness and role alignment.",
      hints: {
        structure: "Choose 2-3 relevant strengths with specific examples",
        keyPoints: ["Role-relevant strengths", "Concrete examples", "Impact on work"],
        commonMistakes: ["Generic strengths", "No examples", "Irrelevant to role"]
      },
      followUpQuestions: ["Can you give me a specific example?"],
      industrySpecific: false,
      tags: ["strengths", "self-awareness"],
      isRequired: true
    },
    {
      questionText: "Where do you see yourself in 5 years?",
      questionType: "BEHAVIORAL",
      category: "GENERAL",
      difficulty: "INTERMEDIATE",
      expectedDuration: 120,
      context: "Assesses career planning and long-term commitment.",
      hints: {
        structure: "Show growth mindset while staying realistic",
        keyPoints: ["Career progression", "Skill development", "Value to company"],
        commonMistakes: ["Unrealistic goals", "Vague answers", "Job-hopping implications"]
      },
      followUpQuestions: ["How does this role fit into your career plan?"],
      industrySpecific: false,
      tags: ["career-goals", "planning"],
      isRequired: true
    }
  ];

  // Return the requested number of questions, cycling through if needed
  const selectedQuestions = [];
  for (let i = 0; i < count; i++) {
    const question = { ...fallbackQuestions[i % fallbackQuestions.length] };
    // Adjust difficulty if specified
    if (difficulty !== 'INTERMEDIATE') {
      question.difficulty = difficulty;
    }
    selectedQuestions.push(question);
  }

  return selectedQuestions;
}

// Validation schema for generating questions
const generateQuestionsSchema = z.object({
  count: z.number().min(1).max(20).default(10),
  questionTypes: z.array(z.enum(['BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC'])).optional(),
  categories: z.array(z.enum(['GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE'])).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
});

// GET - Retrieve questions for a session
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        // Verify session ownership
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Get questions with user responses
        const questions = await prisma.interviewQuestion.findMany({
          where: { sessionId },
          include: {
            responses: {
              where: { userId },
              select: {
                id: true,
                responseText: true,
                audioUrl: true,
                responseTime: true,
                preparationTime: true,
                aiScore: true,
                isCompleted: true,
                userNotes: true,
                createdAt: true,
              },
            },
          },
          orderBy: { questionOrder: 'asc' },
        });

        return NextResponse.json({
          success: true,
          data: questions,
        });
      } catch (error) {
        console.error('Error fetching interview questions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview questions' },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Generate questions for a session using AI
export const POST = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 generations per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        const body = await request.json();
        const validation = generateQuestionsSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const { count, questionTypes, categories, difficulty } = validation.data;

        // Verify session ownership and get session details
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Check if questions already exist
        const existingQuestions = await prisma.interviewQuestion.count({
          where: { sessionId },
        });

        if (existingQuestions > 0) {
          return NextResponse.json(
            { success: false, error: 'Questions already exist for this session' },
            { status: 400 }
          );
        }

        // Generate questions using AI
        const questionsResult = await geminiService.generateInterviewQuestions({
          sessionType: interviewSession.sessionType,
          careerPath: interviewSession.careerPath || undefined,
          experienceLevel: interviewSession.experienceLevel || undefined,
          companyType: interviewSession.companyType || undefined,
          industryFocus: interviewSession.industryFocus || undefined,
          specificRole: interviewSession.specificRole || undefined,
          interviewType: interviewSession.interviewType || undefined,
          focusAreas: interviewSession.focusAreas,
          difficulty: difficulty || interviewSession.difficulty,
          questionTypes,
          categories,
          count,
        });

        if (!questionsResult.success) {
          // Fallback to default questions if AI service fails
          console.log('AI service failed, using fallback questions:', questionsResult.error);
          questionsResult.data = {
            questions: generateFallbackQuestions(count, difficulty || interviewSession.difficulty),
            metadata: {
              source: 'fallback',
              reason: 'AI service unavailable',
              timestamp: new Date().toISOString()
            }
          };
        }

        // Validate the response structure
        if (!questionsResult.data || !questionsResult.data.questions || !Array.isArray(questionsResult.data.questions)) {
          console.error('Invalid AI response structure, using fallback questions:', JSON.stringify(questionsResult, null, 2));
          questionsResult.data = {
            questions: generateFallbackQuestions(count, difficulty || interviewSession.difficulty),
            metadata: {
              source: 'fallback',
              reason: 'Invalid AI response structure',
              timestamp: new Date().toISOString()
            }
          };
        }

        if (questionsResult.data.questions.length === 0) {
          // Fallback to default questions if AI returns empty
          console.log('AI returned no questions, using fallback questions');
          questionsResult.data.questions = generateFallbackQuestions(count, difficulty || interviewSession.difficulty);
        }

        // Validate and map enum values
        const validateQuestionType = (type: string): string => {
          const validTypes = ['BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC'];
          if (validTypes.includes(type)) return type;

          // Map common invalid values
          const typeMapping: { [key: string]: string } = {
            'TECHNICAL_SKILLS': 'TECHNICAL',
            'SOFT_SKILLS': 'BEHAVIORAL',
            'TEAMWORK': 'BEHAVIORAL',
            'ADAPTABILITY': 'BEHAVIORAL',
            'GENERAL': 'BEHAVIORAL'
          };

          return typeMapping[type] || 'BEHAVIORAL';
        };

        const validateCategory = (category: string): string => {
          const validCategories = ['GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE'];
          return validCategories.includes(category) ? category : 'GENERAL';
        };

        const validateDifficulty = (diff: string): string => {
          const validDifficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
          return validDifficulties.includes(diff) ? diff : 'INTERMEDIATE';
        };

        // Save generated questions to database
        const questionsData = questionsResult.data.questions.map((q: any, index: number) => ({
          sessionId,
          questionText: q.questionText,
          questionType: validateQuestionType(q.questionType),
          category: validateCategory(q.category),
          difficulty: validateDifficulty(q.difficulty || difficulty || interviewSession.difficulty),
          expectedDuration: q.expectedDuration || 180,
          context: q.context,
          hints: q.hints,
          followUpQuestions: q.followUpQuestions,
          industrySpecific: q.industrySpecific || false,
          questionOrder: index + 1,
          isRequired: q.isRequired !== false,
          tags: q.tags,
        }));

        const createdQuestions = await prisma.interviewQuestion.createMany({
          data: questionsData,
        });

        // Update session with total questions count
        await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            totalQuestions: questionsData.length,
            lastActiveAt: new Date(),
          },
        });

        // Fetch the created questions with full details
        const questions = await prisma.interviewQuestion.findMany({
          where: { sessionId },
          orderBy: { questionOrder: 'asc' },
        });

        return NextResponse.json({
          success: true,
          data: {
            questions,
            metadata: questionsResult.data.metadata,
          },
          message: `Generated ${questions.length} interview questions successfully`,
        });
      } catch (error) {
        console.error('Error generating interview questions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to generate interview questions' },
          { status: 500 }
        );
      }
    }
  );
});
