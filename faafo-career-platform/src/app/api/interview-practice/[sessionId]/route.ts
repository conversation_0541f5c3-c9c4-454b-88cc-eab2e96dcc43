import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { with<PERSON>rror<PERSON>and<PERSON> } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Validation schema for updating session
const updateSessionSchema = z.object({
  status: z.enum(['IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).optional(),
  timeSpent: z.number().min(0).optional(),
  overallScore: z.number().min(0).max(10).optional(),
  aiInsights: z.any().optional(),
});

// GET - Retrieve specific interview session
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
          include: {
            questions: {
              include: {
                responses: {
                  where: { userId },
                  select: {
                    id: true,
                    responseText: true,
                    audioUrl: true,
                    responseTime: true,
                    preparationTime: true,
                    aiScore: true,
                    aiAnalysis: true,
                    feedback: true,
                    isCompleted: true,
                    userNotes: true,
                    createdAt: true,
                    updatedAt: true,
                  },
                },
              },
              orderBy: { questionOrder: 'asc' },
            },
            responses: {
              where: { userId },
              select: {
                id: true,
                questionId: true,
                isCompleted: true,
                aiScore: true,
                responseTime: true,
              },
            },
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Calculate progress
        const completedQuestions = interviewSession.responses.filter(r => r.isCompleted).length;
        const progressPercentage = interviewSession.totalQuestions > 0 
          ? Math.round((completedQuestions / interviewSession.totalQuestions) * 100)
          : 0;

        const sessionWithProgress = {
          ...interviewSession,
          progress: {
            completed: completedQuestions,
            total: interviewSession.totalQuestions,
            percentage: progressPercentage,
          },
        };

        return NextResponse.json({
          success: true,
          data: sessionWithProgress,
        });
      } catch (error) {
        console.error('Error fetching interview session:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview session' },
          { status: 500 }
        );
      }
    }
  );
}

// PATCH - Update interview session
export const PATCH = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 updates per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        const body = await request.json();
        const validation = updateSessionSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const updateData = validation.data;

        // Verify session ownership
        const existingSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!existingSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Update session
        const updatedSession = await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            ...updateData,
            lastActiveAt: new Date(),
            ...(updateData.status === 'COMPLETED' && !existingSession.completedAt 
              ? { completedAt: new Date() } 
              : {}),
          },
          include: {
            questions: {
              select: {
                id: true,
                questionType: true,
                category: true,
                difficulty: true,
                questionOrder: true,
              },
              orderBy: { questionOrder: 'asc' }
            },
            responses: {
              where: { userId },
              select: {
                id: true,
                questionId: true,
                isCompleted: true,
                aiScore: true,
              },
            },
          },
        });

        return NextResponse.json({
          success: true,
          data: updatedSession,
          message: 'Interview session updated successfully',
        });
      } catch (error) {
        console.error('Error updating interview session:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to update interview session' },
          { status: 500 }
        );
      }
    }
  );
});

// DELETE - Delete interview session
export const DELETE = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        // Verify session ownership
        const existingSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!existingSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Delete session (cascade will handle questions and responses)
        await prisma.interviewSession.delete({
          where: { id: sessionId },
        });

        return NextResponse.json({
          success: true,
          message: 'Interview session deleted successfully',
        });
      } catch (error) {
        console.error('Error deleting interview session:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to delete interview session' },
          { status: 500 }
        );
      }
    }
  );
});
