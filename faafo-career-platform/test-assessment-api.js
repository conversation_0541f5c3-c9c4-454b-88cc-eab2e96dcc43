// Simple test script to check assessment API endpoints
const fetch = require('node-fetch');

async function testAssessmentAPI() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing Assessment API endpoints...\n');
  
  // Test 1: GET /api/assessment without authentication
  try {
    console.log('1. Testing GET /api/assessment (unauthenticated)');
    const response = await fetch(`${baseUrl}/api/assessment`);
    console.log(`   Status: ${response.status}`);
    const data = await response.json();
    console.log(`   Response:`, data);
    console.log('   Expected: 401 Unauthorized\n');
  } catch (error) {
    console.log(`   Error: ${error.message}\n`);
  }
  
  // Test 2: Check if the assessment page loads (should redirect to login)
  try {
    console.log('2. Testing GET /assessment page');
    const response = await fetch(`${baseUrl}/assessment`);
    console.log(`   Status: ${response.status}`);
    console.log(`   Content-Type: ${response.headers.get('content-type')}`);
    console.log('   Expected: HTML page with login redirect or loading state\n');
  } catch (error) {
    console.log(`   Error: ${error.message}\n`);
  }
  
  // Test 3: Check if the API route exists
  try {
    console.log('3. Testing API route existence');
    const response = await fetch(`${baseUrl}/api/assessment`, {
      method: 'OPTIONS'
    });
    console.log(`   Status: ${response.status}`);
    console.log('   Expected: Should not be 404\n');
  } catch (error) {
    console.log(`   Error: ${error.message}\n`);
  }
}

testAssessmentAPI().catch(console.error);
