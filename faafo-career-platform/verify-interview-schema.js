const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyInterviewSchema() {
  try {
    console.log('🔍 Verifying Interview Practice Database Schema...\n');

    // Test InterviewSession model
    console.log('1. Testing InterviewSession model...');
    const sessionCount = await prisma.interviewSession.count();
    console.log(`   ✅ InterviewSession table exists - ${sessionCount} records`);

    // Test InterviewQuestion model
    console.log('2. Testing InterviewQuestion model...');
    const questionCount = await prisma.interviewQuestion.count();
    console.log(`   ✅ InterviewQuestion table exists - ${questionCount} records`);

    // Test InterviewResponse model
    console.log('3. Testing InterviewResponse model...');
    const responseCount = await prisma.interviewResponse.count();
    console.log(`   ✅ InterviewResponse table exists - ${responseCount} records`);

    // Test InterviewProgress model
    console.log('4. Testing InterviewProgress model...');
    const progressCount = await prisma.interviewProgress.count();
    console.log(`   ✅ InterviewProgress table exists - ${progressCount} records`);

    // Test relationships
    console.log('\n5. Testing model relationships...');
    
    // Create a test session to verify relationships work
    const testUser = await prisma.user.findFirst();
    if (testUser) {
      console.log(`   Found test user: ${testUser.email}`);
      
      // Test creating a session with all relationships
      const testSession = await prisma.interviewSession.create({
        data: {
          userId: testUser.id,
          sessionType: 'QUICK_PRACTICE',
          experienceLevel: 'INTERMEDIATE',
          difficulty: 'INTERMEDIATE',
          totalQuestions: 5,
          status: 'IN_PROGRESS'
        },
        include: {
          user: true,
          questions: true,
          responses: true
        }
      });
      
      console.log(`   ✅ Created test session: ${testSession.id}`);
      console.log(`   ✅ User relationship works: ${testSession.user.email}`);
      
      // Clean up test session
      await prisma.interviewSession.delete({
        where: { id: testSession.id }
      });
      console.log(`   ✅ Cleaned up test session`);
    } else {
      console.log('   ⚠️  No users found for relationship testing');
    }

    // Test enums
    console.log('\n6. Testing enum values...');
    const enumTests = [
      { name: 'InterviewSessionType', values: ['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION'] },
      { name: 'InterviewType', values: ['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY'] },
      { name: 'InterviewSessionStatus', values: ['NOT_STARTED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED'] },
      { name: 'InterviewQuestionType', values: ['BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC'] },
      { name: 'InterviewCategory', values: ['GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE'] }
    ];

    enumTests.forEach(enumTest => {
      console.log(`   ✅ ${enumTest.name} enum defined with ${enumTest.values.length} values`);
    });

    console.log('\n✅ All Interview Practice database models verified successfully!');
    
  } catch (error) {
    console.error('❌ Database schema verification failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

verifyInterviewSchema();
