#!/usr/bin/env npx tsx

/**
 * Final Resources Tab Test
 * Comprehensive test of all Resources Tab functionality
 */

import { chromium, Browser, Page } from 'playwright';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  details: string;
}

class FinalResourcesTest {
  private browser!: Browser;
  private page!: Page;
  private results: TestResult[] = [];
  private baseUrl = 'http://localhost:3005';

  async init() {
    this.browser = await chromium.launch({ headless: false });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }

  async cleanup() {
    await this.browser.close();
  }

  private addResult(test: string, status: 'PASS' | 'FAIL', details: string) {
    this.results.push({ test, status, details });
    const emoji = status === 'PASS' ? '✅' : '❌';
    console.log(`${emoji} ${test}: ${details}`);
  }

  async testCompleteFlow() {
    console.log('🚀 Final Resources Tab Test\n');
    
    try {
      // 1. Test main resources page
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });
      
      const title = await this.page.title();
      if (title.includes('FAAFO')) {
        this.addResult('Main Page Load', 'PASS', 'Resources page loads successfully');
      } else {
        this.addResult('Main Page Load', 'FAIL', 'Page failed to load');
      }

      // 2. Test resource type tabs
      await this.page.click('button:has-text("Mindset & Support")');
      await this.page.waitForTimeout(1000);
      const mindsetResources = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Mindset Tab', 'PASS', `Shows ${mindsetResources} mindset resources`);

      await this.page.click('button:has-text("Skill Development")');
      await this.page.waitForTimeout(1000);
      const skillResources = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Skill Development Tab', 'PASS', `Shows ${skillResources} skill resources`);

      await this.page.click('button:has-text("All Resources")');
      await this.page.waitForTimeout(1000);

      // 3. Test search functionality
      await this.page.fill('input[placeholder*="Search"]', 'cybersecurity');
      await this.page.waitForTimeout(1000);
      const searchResults = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Search Function', 'PASS', `Found ${searchResults} cybersecurity resources`);

      // Clear search
      await this.page.fill('input[placeholder*="Search"]', '');
      await this.page.waitForTimeout(1000);

      // 4. Test resource card buttons
      const firstCard = await this.page.locator('[class*="grid"] > div').first();
      const viewDetailsBtn = firstCard.locator('a:has-text("View Details")');
      const externalBtn = firstCard.locator('a[target="_blank"]');
      
      if (await viewDetailsBtn.isVisible() && await externalBtn.isVisible()) {
        this.addResult('Resource Card Buttons', 'PASS', 'Both View Details and External buttons present');
      } else {
        this.addResult('Resource Card Buttons', 'FAIL', 'Missing buttons on resource cards');
      }

      // 5. Test resource detail page
      await viewDetailsBtn.click();
      await this.page.waitForLoadState('networkidle');
      
      const detailTitle = await this.page.locator('h1').textContent();
      if (detailTitle && detailTitle.length > 0) {
        this.addResult('Resource Detail Page', 'PASS', `Detail page loads: "${detailTitle}"`);
      } else {
        this.addResult('Resource Detail Page', 'FAIL', 'Detail page failed to load');
      }

      // 6. Test back navigation (link or button)
      const backLink = await this.page.locator('a:has-text("Back to Resources")');
      const backButton = await this.page.locator('button:has-text("Back")');
      const backButtonWithLink = await this.page.locator('button:has(a:has-text("Back to Resources"))');

      if (await backLink.isVisible() || await backButton.isVisible() || await backButtonWithLink.isVisible()) {
        this.addResult('Back Navigation', 'PASS', 'Back navigation present on detail page');
        if (await backLink.isVisible()) {
          await backLink.click();
        } else if (await backButtonWithLink.isVisible()) {
          await backButtonWithLink.click();
        } else {
          await backButton.click();
        }
        await this.page.waitForLoadState('networkidle');
      } else {
        this.addResult('Back Navigation', 'FAIL', 'Back navigation missing');
      }

      // 7. Test start learning button on static resource
      await this.page.goto(`${this.baseUrl}/resources/1`, { waitUntil: 'networkidle' });
      await this.page.waitForTimeout(2000); // Wait for page to fully load
      const startBtn = await this.page.locator('button:has-text("Start Learning")');
      if (await startBtn.isVisible()) {
        this.addResult('Start Learning Button', 'PASS', 'Start Learning button present');
      } else {
        this.addResult('Start Learning Button', 'FAIL', 'Start Learning button missing');
      }

      // 7b. Test back button on static resource detail page
      const staticBackBtn = await this.page.locator('a:has-text("Back to Resources")');
      if (await staticBackBtn.isVisible()) {
        this.addResult('Back Button on Static Resource', 'PASS', 'Back button present on static resource detail page');
      } else {
        this.addResult('Back Button on Static Resource', 'FAIL', 'Back button missing on static resource detail page');
      }

      // 8. Test navigation consistency
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      const navResourcesLink = await this.page.locator('a[href="/resources"]').first();
      if (await navResourcesLink.isVisible()) {
        this.addResult('Navigation Consistency', 'PASS', 'Resources link in main navigation');
      } else {
        this.addResult('Navigation Consistency', 'FAIL', 'Navigation link missing');
      }

      // 9. Test theme consistency (no blue colors in resources content)
      const resourcesContent = this.page.locator('main');
      const blueElements = await resourcesContent.locator('[class*="blue-"], [class*="bg-blue"], [class*="text-blue"]').count();
      if (blueElements === 0) {
        this.addResult('Theme Consistency', 'PASS', 'No blue colors found in resources content - theme consistent');
      } else {
        this.addResult('Theme Consistency', 'FAIL', `Found ${blueElements} blue elements in resources content`);
      }

      // 10. Test filter functionality
      await this.page.selectOption('select', { index: 1 }); // Select first category
      await this.page.waitForTimeout(1000);
      const filteredResults = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Filter Function', 'PASS', `Filter works, showing ${filteredResults} filtered resources`);

    } catch (error) {
      this.addResult('Test Execution', 'FAIL', `Error during test: ${error}`);
    }
  }

  async run() {
    await this.init();
    
    try {
      await this.testCompleteFlow();
    } catch (error) {
      console.error('Test error:', error);
    } finally {
      await this.cleanup();
    }

    // Print summary
    console.log('\n📊 FINAL TEST SUMMARY');
    console.log('=====================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;
    const successRate = Math.round((passed / total) * 100);
    
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`📊 Success Rate: ${successRate}%`);
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.filter(r => r.status === 'FAIL').forEach(r => {
        console.log(`   • ${r.test}: ${r.details}`);
      });
    }
    
    console.log('\n🎯 Resources Tab Testing Complete!');
    return this.results;
  }
}

// Run the test
if (require.main === module) {
  const test = new FinalResourcesTest();
  test.run().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

export default FinalResourcesTest;
