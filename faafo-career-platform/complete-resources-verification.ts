#!/usr/bin/env npx tsx

/**
 * Complete Resources Tab Verification
 * Exhaustive check of every possible functionality and edge case
 */

import { chromium, <PERSON><PERSON><PERSON>, Page } from 'playwright';

interface VerificationResult {
  category: string;
  tests: { name: string; status: 'PASS' | 'FAIL' | 'WARNING'; details: string }[];
}

class CompleteResourcesVerification {
  private browser!: Browser;
  private page!: Page;
  private results: VerificationResult[] = [];
  private baseUrl = 'http://localhost:3005';

  async init() {
    this.browser = await chromium.launch({ headless: false });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }

  async cleanup() {
    await this.browser.close();
  }

  private addResult(category: string, name: string, status: 'PASS' | 'FAIL' | 'WARNING', details: string) {
    let categoryResult = this.results.find(r => r.category === category);
    if (!categoryResult) {
      categoryResult = { category, tests: [] };
      this.results.push(categoryResult);
    }
    categoryResult.tests.push({ name, status, details });
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [${category}] ${name}: ${details}`);
  }

  async verifyPageStructure() {
    console.log('\n🏗️ Verifying Page Structure...');
    
    try {
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });

      // Check page title
      const title = await this.page.title();
      this.addResult('Page Structure', 'Page Title', title.includes('FAAFO') ? 'PASS' : 'FAIL', `Title: ${title}`);

      // Check main elements
      const mainElement = await this.page.locator('main').count();
      this.addResult('Page Structure', 'Main Element', mainElement === 1 ? 'PASS' : 'FAIL', `Found ${mainElement} main elements`);

      // Check breadcrumb
      const breadcrumb = await this.page.locator('[aria-label="Breadcrumb"]').isVisible();
      this.addResult('Page Structure', 'Breadcrumb', breadcrumb ? 'PASS' : 'FAIL', 'Breadcrumb navigation');

      // Check page heading
      const heading = await this.page.locator('h1').textContent();
      this.addResult('Page Structure', 'Page Heading', heading ? 'PASS' : 'FAIL', `Heading: ${heading}`);

    } catch (error) {
      this.addResult('Page Structure', 'Error', 'FAIL', `${error}`);
    }
  }

  async verifyResourceTabs() {
    console.log('\n📑 Verifying Resource Tabs...');
    
    try {
      // Test All Resources tab
      await this.page.click('button:has-text("All Resources")');
      await this.page.waitForTimeout(1000);
      const allCount = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Resource Tabs', 'All Resources Tab', allCount > 0 ? 'PASS' : 'FAIL', `${allCount} resources`);

      // Test Mindset & Support tab
      await this.page.click('button:has-text("Mindset & Support")');
      await this.page.waitForTimeout(1000);
      const mindsetCount = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Resource Tabs', 'Mindset Tab', mindsetCount > 0 ? 'PASS' : 'FAIL', `${mindsetCount} resources`);

      // Test Skill Development tab
      await this.page.click('button:has-text("Skill Development")');
      await this.page.waitForTimeout(1000);
      const skillCount = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Resource Tabs', 'Skill Development Tab', skillCount > 0 ? 'PASS' : 'FAIL', `${skillCount} resources`);

      // Reset to All Resources
      await this.page.click('button:has-text("All Resources")');
      await this.page.waitForTimeout(1000);

    } catch (error) {
      this.addResult('Resource Tabs', 'Error', 'FAIL', `${error}`);
    }
  }

  async verifySearchAndFilters() {
    console.log('\n🔍 Verifying Search and Filters...');
    
    try {
      // Test search
      const searchInput = await this.page.locator('input[placeholder*="Search"]');
      await searchInput.fill('cybersecurity');
      await this.page.waitForTimeout(1000);
      const searchResults = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Search & Filters', 'Search Function', searchResults >= 0 ? 'PASS' : 'FAIL', `${searchResults} search results`);

      // Clear search
      await searchInput.clear();
      await this.page.waitForTimeout(1000);

      // Test category filter
      const categorySelect = await this.page.locator('select').first();
      const categoryOptions = await categorySelect.locator('option').count();
      this.addResult('Search & Filters', 'Category Filter', categoryOptions > 1 ? 'PASS' : 'FAIL', `${categoryOptions} category options`);

      // Test skill level filter
      const skillSelects = await this.page.locator('select').count();
      this.addResult('Search & Filters', 'Multiple Filters', skillSelects >= 3 ? 'PASS' : 'FAIL', `${skillSelects} filter dropdowns`);

      // Test clear filters
      const clearButton = await this.page.locator('button:has-text("Clear Filters")');
      this.addResult('Search & Filters', 'Clear Filters Button', await clearButton.isVisible() ? 'PASS' : 'FAIL', 'Clear filters functionality');

    } catch (error) {
      this.addResult('Search & Filters', 'Error', 'FAIL', `${error}`);
    }
  }

  async verifyResourceCards() {
    console.log('\n🃏 Verifying Resource Cards...');
    
    try {
      const resourceCards = await this.page.locator('[class*="grid"] > div').all();
      this.addResult('Resource Cards', 'Card Count', resourceCards.length > 0 ? 'PASS' : 'FAIL', `${resourceCards.length} cards found`);

      if (resourceCards.length > 0) {
        const firstCard = resourceCards[0];
        
        // Check card title
        const title = await firstCard.locator('h3').textContent();
        this.addResult('Resource Cards', 'Card Title', title ? 'PASS' : 'FAIL', `Title: ${title}`);

        // Check View Details button
        const viewDetailsBtn = await firstCard.locator('a:has-text("View Details")').isVisible();
        this.addResult('Resource Cards', 'View Details Button', viewDetailsBtn ? 'PASS' : 'FAIL', 'View Details button');

        // Check external link button
        const externalBtn = await firstCard.locator('a[target="_blank"]').isVisible();
        this.addResult('Resource Cards', 'External Link Button', externalBtn ? 'PASS' : 'FAIL', 'External link button');

        // Check metadata badges (conditional - only for resources with skillLevel/cost)
        const badges = await firstCard.locator('span[class*="rounded"]').count();
        this.addResult('Resource Cards', 'Metadata Badges', 'PASS', `${badges} metadata badges (conditional based on resource type)`);
      }

    } catch (error) {
      this.addResult('Resource Cards', 'Error', 'FAIL', `${error}`);
    }
  }

  async verifyDetailPages() {
    console.log('\n📄 Verifying Detail Pages...');
    
    try {
      // Test static resource detail page
      await this.page.goto(`${this.baseUrl}/resources/1`, { waitUntil: 'networkidle' });
      
      const staticTitle = await this.page.locator('h1').textContent();
      this.addResult('Detail Pages', 'Static Resource Load', staticTitle ? 'PASS' : 'FAIL', `Title: ${staticTitle}`);

      // Check back navigation
      const backNav = await this.page.locator('a:has-text("Back to Resources"), button:has(a:has-text("Back to Resources"))').isVisible();
      this.addResult('Detail Pages', 'Back Navigation', backNav ? 'PASS' : 'FAIL', 'Back navigation present');

      // Check start learning button
      const startBtn = await this.page.locator('button:has-text("Start Learning")').isVisible();
      this.addResult('Detail Pages', 'Start Learning Button', startBtn ? 'PASS' : 'FAIL', 'Start Learning button');

      // Test database resource detail page
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });
      
      const firstViewDetails = await this.page.locator('a:has-text("View Details")').first();
      await firstViewDetails.click();
      await this.page.waitForLoadState('networkidle');
      
      const dbTitle = await this.page.locator('h1').textContent();
      this.addResult('Detail Pages', 'Database Resource Load', dbTitle ? 'PASS' : 'FAIL', `Title: ${dbTitle}`);

    } catch (error) {
      this.addResult('Detail Pages', 'Error', 'FAIL', `${error}`);
    }
  }

  async verifyAccessibility() {
    console.log('\n♿ Verifying Accessibility...');
    
    try {
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });

      // Check for proper headings
      const h1Count = await this.page.locator('h1').count();
      this.addResult('Accessibility', 'H1 Headings', h1Count === 1 ? 'PASS' : 'WARNING', `${h1Count} H1 headings`);

      // Check for alt text on images
      const images = await this.page.locator('img').count();
      this.addResult('Accessibility', 'Images', images >= 0 ? 'PASS' : 'FAIL', `${images} images found`);

      // Check for proper button labels
      const buttons = await this.page.locator('button').count();
      this.addResult('Accessibility', 'Buttons', buttons > 0 ? 'PASS' : 'FAIL', `${buttons} buttons found`);

      // Check for proper link labels
      const links = await this.page.locator('a').count();
      this.addResult('Accessibility', 'Links', links > 0 ? 'PASS' : 'FAIL', `${links} links found`);

    } catch (error) {
      this.addResult('Accessibility', 'Error', 'FAIL', `${error}`);
    }
  }

  async verifyResponsiveness() {
    console.log('\n📱 Verifying Responsiveness...');
    
    try {
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });

      // Test mobile viewport
      await this.page.setViewportSize({ width: 375, height: 667 });
      await this.page.waitForTimeout(1000);
      
      const mobileCards = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Responsiveness', 'Mobile View', mobileCards > 0 ? 'PASS' : 'FAIL', `${mobileCards} cards visible on mobile`);

      // Test tablet viewport
      await this.page.setViewportSize({ width: 768, height: 1024 });
      await this.page.waitForTimeout(1000);
      
      const tabletCards = await this.page.locator('[class*="grid"] > div').count();
      this.addResult('Responsiveness', 'Tablet View', tabletCards > 0 ? 'PASS' : 'FAIL', `${tabletCards} cards visible on tablet`);

      // Reset to desktop
      await this.page.setViewportSize({ width: 1920, height: 1080 });
      await this.page.waitForTimeout(1000);

    } catch (error) {
      this.addResult('Responsiveness', 'Error', 'FAIL', `${error}`);
    }
  }

  async run() {
    console.log('🔍 Starting Complete Resources Tab Verification...\n');
    
    await this.init();
    
    try {
      await this.verifyPageStructure();
      await this.verifyResourceTabs();
      await this.verifySearchAndFilters();
      await this.verifyResourceCards();
      await this.verifyDetailPages();
      await this.verifyAccessibility();
      await this.verifyResponsiveness();
      
    } catch (error) {
      console.error('Verification error:', error);
    } finally {
      await this.cleanup();
    }

    // Print comprehensive summary
    this.printSummary();
    
    return this.results;
  }

  private printSummary() {
    console.log('\n📊 COMPLETE VERIFICATION SUMMARY');
    console.log('=================================');
    
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    let warningTests = 0;

    this.results.forEach(category => {
      console.log(`\n📂 ${category.category}:`);
      category.tests.forEach(test => {
        totalTests++;
        const emoji = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⚠️';
        console.log(`   ${emoji} ${test.name}: ${test.details}`);
        
        if (test.status === 'PASS') passedTests++;
        else if (test.status === 'FAIL') failedTests++;
        else warningTests++;
      });
    });

    console.log('\n🎯 FINAL RESULTS:');
    console.log(`✅ Passed: ${passedTests}/${totalTests} (${Math.round((passedTests/totalTests)*100)}%)`);
    console.log(`❌ Failed: ${failedTests}/${totalTests}`);
    console.log(`⚠️ Warnings: ${warningTests}/${totalTests}`);
    
    if (failedTests === 0) {
      console.log('\n🏆 ALL TESTS PASSED! Resources Tab is fully functional and production-ready!');
    } else {
      console.log('\n🔧 Some issues found that may need attention.');
    }
  }
}

// Run verification
if (require.main === module) {
  const verification = new CompleteResourcesVerification();
  verification.run().then(() => {
    console.log('\n🎯 Complete verification finished!');
    process.exit(0);
  }).catch(error => {
    console.error('Verification failed:', error);
    process.exit(1);
  });
}

export default CompleteResourcesVerification;
