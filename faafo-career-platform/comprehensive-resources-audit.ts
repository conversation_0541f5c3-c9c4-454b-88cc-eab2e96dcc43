#!/usr/bin/env npx tsx

/**
 * Comprehensive Resources Tab Audit
 * Tests every file, button, redirection, flow, resource validity, and UI consistency
 */

import { chromium, Browser, Page } from 'playwright';
import { getAllStaticResources, getMindsetResources, getLearningResources } from './src/lib/staticResources';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  details: string;
  screenshot?: string;
}

class ResourcesAudit {
  private browser!: Browser;
  private page!: Page;
  private results: TestResult[] = [];
  private baseUrl = 'http://localhost:3005';

  async init() {
    this.browser = await chromium.launch({ headless: false });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }

  async cleanup() {
    await this.browser.close();
  }

  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', details: string) {
    this.results.push({ test, status, details });
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${details}`);
  }

  async testPageLoad() {
    console.log('\n🔍 Testing Page Load and Basic Structure...');
    
    try {
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      
      // Check if page loads without errors
      const title = await this.page.title();
      if (title.includes('FAAFO')) {
        this.addResult('Page Load', 'PASS', 'Resources page loads successfully');
      } else {
        this.addResult('Page Load', 'FAIL', `Unexpected title: ${title}`);
      }

      // Check for loading spinner disappearance
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });
      this.addResult('Loading State', 'PASS', 'Loading spinner disappears');

      // Check main content appears
      const mainContent = await this.page.locator('main').isVisible();
      if (mainContent) {
        this.addResult('Main Content', 'PASS', 'Main content is visible');
      } else {
        this.addResult('Main Content', 'FAIL', 'Main content not visible');
      }

    } catch (error) {
      this.addResult('Page Load', 'FAIL', `Error loading page: ${error}`);
    }
  }

  async testNavigation() {
    console.log('\n🧭 Testing Navigation Elements...');

    try {
      // Test breadcrumb navigation
      const breadcrumb = await this.page.locator('[aria-label="Breadcrumb"]').isVisible();
      if (breadcrumb) {
        this.addResult('Breadcrumb', 'PASS', 'Breadcrumb navigation is present');
      } else {
        this.addResult('Breadcrumb', 'FAIL', 'Breadcrumb navigation missing');
      }

      // Test main navigation links
      const resourcesLink = await this.page.locator('a[href="/resources"]').first();
      if (await resourcesLink.isVisible()) {
        this.addResult('Navigation Link', 'PASS', 'Resources link in navigation');
      } else {
        this.addResult('Navigation Link', 'FAIL', 'Resources link missing from navigation');
      }

      // Test home link in breadcrumb
      const homeLink = await this.page.locator('a[href="/"]').first();
      if (await homeLink.isVisible()) {
        await homeLink.click();
        await this.page.waitForURL('**/');
        this.addResult('Home Navigation', 'PASS', 'Home link works from breadcrumb');
        
        // Navigate back to resources
        await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
        await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });
      } else {
        this.addResult('Home Navigation', 'FAIL', 'Home link not working');
      }

    } catch (error) {
      this.addResult('Navigation', 'FAIL', `Navigation error: ${error}`);
    }
  }

  async testResourceTypeTabs() {
    console.log('\n📑 Testing Resource Type Tabs...');

    try {
      // Test "All Resources" tab
      const allResourcesTab = await this.page.locator('button:has-text("All Resources")');
      if (await allResourcesTab.isVisible()) {
        await allResourcesTab.click();
        await this.page.waitForTimeout(1000);
        this.addResult('All Resources Tab', 'PASS', 'All Resources tab clickable');
      } else {
        this.addResult('All Resources Tab', 'FAIL', 'All Resources tab not found');
      }

      // Test "Mindset & Support" tab
      const mindsetTab = await this.page.locator('button:has-text("Mindset & Support")');
      if (await mindsetTab.isVisible()) {
        await mindsetTab.click();
        await this.page.waitForTimeout(1000);
        
        // Check if resources are filtered
        const resourceCards = await this.page.locator('[class*="grid"] > div').count();
        this.addResult('Mindset Tab', 'PASS', `Mindset tab works, showing ${resourceCards} resources`);
      } else {
        this.addResult('Mindset Tab', 'FAIL', 'Mindset & Support tab not found');
      }

      // Test "Skill Development" tab
      const skillTab = await this.page.locator('button:has-text("Skill Development")');
      if (await skillTab.isVisible()) {
        await skillTab.click();
        await this.page.waitForTimeout(1000);
        
        const resourceCards = await this.page.locator('[class*="grid"] > div').count();
        this.addResult('Skill Development Tab', 'PASS', `Skill Development tab works, showing ${resourceCards} resources`);
      } else {
        this.addResult('Skill Development Tab', 'FAIL', 'Skill Development tab not found');
      }

      // Reset to All Resources
      await allResourcesTab.click();
      await this.page.waitForTimeout(1000);

    } catch (error) {
      this.addResult('Resource Type Tabs', 'FAIL', `Tab error: ${error}`);
    }
  }

  async testSearchAndFilters() {
    console.log('\n🔍 Testing Search and Filter Functionality...');

    try {
      // Test search functionality
      const searchInput = await this.page.locator('input[placeholder*="Search"]');
      if (await searchInput.isVisible()) {
        await searchInput.fill('cybersecurity');
        await this.page.waitForTimeout(1000);
        
        const searchResults = await this.page.locator('[class*="grid"] > div').count();
        this.addResult('Search Function', 'PASS', `Search works, found ${searchResults} results for 'cybersecurity'`);
        
        // Clear search
        await searchInput.clear();
        await this.page.waitForTimeout(1000);
      } else {
        this.addResult('Search Function', 'FAIL', 'Search input not found');
      }

      // Test category filter
      const categoryFilter = await this.page.locator('select').first();
      if (await categoryFilter.isVisible()) {
        await categoryFilter.selectOption({ index: 1 }); // Select first non-"all" option
        await this.page.waitForTimeout(1000);
        this.addResult('Category Filter', 'PASS', 'Category filter works');
        
        // Reset filter
        await categoryFilter.selectOption({ value: 'all' });
        await this.page.waitForTimeout(1000);
      } else {
        this.addResult('Category Filter', 'FAIL', 'Category filter not found');
      }

      // Test Clear Filters button
      const clearButton = await this.page.locator('button:has-text("Clear Filters")');
      if (await clearButton.isVisible()) {
        await clearButton.click();
        await this.page.waitForTimeout(1000);
        this.addResult('Clear Filters', 'PASS', 'Clear Filters button works');
      } else {
        this.addResult('Clear Filters', 'FAIL', 'Clear Filters button not found');
      }

    } catch (error) {
      this.addResult('Search and Filters', 'FAIL', `Filter error: ${error}`);
    }
  }

  async testResourceCards() {
    console.log('\n🃏 Testing Resource Cards and Buttons...');

    try {
      // Get all resource cards
      const resourceCards = await this.page.locator('[class*="grid"] > div').all();
      
      if (resourceCards.length === 0) {
        this.addResult('Resource Cards', 'FAIL', 'No resource cards found');
        return;
      }

      this.addResult('Resource Cards', 'PASS', `Found ${resourceCards.length} resource cards`);

      // Test first few resource cards
      for (let i = 0; i < Math.min(3, resourceCards.length); i++) {
        const card = resourceCards[i];
        
        // Check if card has title
        const title = await card.locator('h3').textContent();
        if (title) {
          this.addResult(`Card ${i + 1} Title`, 'PASS', `Title: "${title}"`);
        } else {
          this.addResult(`Card ${i + 1} Title`, 'FAIL', 'No title found');
        }

        // Check for "View Details" button
        const viewDetailsBtn = card.locator('a:has-text("View Details")');
        if (await viewDetailsBtn.isVisible()) {
          this.addResult(`Card ${i + 1} View Details`, 'PASS', 'View Details button present');
        } else {
          this.addResult(`Card ${i + 1} View Details`, 'FAIL', 'View Details button missing');
        }

        // Check for external link button
        const externalBtn = card.locator('a[target="_blank"]');
        if (await externalBtn.isVisible()) {
          this.addResult(`Card ${i + 1} External Link`, 'PASS', 'External link button present');
        } else {
          this.addResult(`Card ${i + 1} External Link`, 'FAIL', 'External link button missing');
        }
      }

    } catch (error) {
      this.addResult('Resource Cards', 'FAIL', `Card error: ${error}`);
    }
  }

  async testResourceDetailPages() {
    console.log('\n📄 Testing Resource Detail Pages...');

    try {
      // Test static resource detail page
      await this.page.goto(`${this.baseUrl}/resources/1`, { waitUntil: 'networkidle' });
      
      const detailTitle = await this.page.locator('h1').textContent();
      if (detailTitle) {
        this.addResult('Static Resource Detail', 'PASS', `Detail page loads: "${detailTitle}"`);
      } else {
        this.addResult('Static Resource Detail', 'FAIL', 'Detail page title not found');
      }

      // Check for back button
      const backButton = await this.page.locator('button:has-text("Back")');
      if (await backButton.isVisible()) {
        this.addResult('Back Button', 'PASS', 'Back button present on detail page');
      } else {
        this.addResult('Back Button', 'FAIL', 'Back button missing');
      }

      // Check for start/external link button
      const startButton = await this.page.locator('a[target="_blank"]').first();
      if (await startButton.isVisible()) {
        this.addResult('Start Button', 'PASS', 'Start/external button present');
      } else {
        this.addResult('Start Button', 'FAIL', 'Start/external button missing');
      }

      // Test database resource detail page
      await this.page.goto(`${this.baseUrl}/resources`, { waitUntil: 'networkidle' });
      await this.page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });
      
      // Click on first "View Details" button
      const firstViewDetails = await this.page.locator('a:has-text("View Details")').first();
      if (await firstViewDetails.isVisible()) {
        await firstViewDetails.click();
        await this.page.waitForLoadState('networkidle');
        
        const dbDetailTitle = await this.page.locator('h1').textContent();
        if (dbDetailTitle) {
          this.addResult('Database Resource Detail', 'PASS', `DB detail page loads: "${dbDetailTitle}"`);
        } else {
          this.addResult('Database Resource Detail', 'FAIL', 'DB detail page title not found');
        }
      }

    } catch (error) {
      this.addResult('Resource Detail Pages', 'FAIL', `Detail page error: ${error}`);
    }
  }

  async run() {
    console.log('🚀 Starting Comprehensive Resources Tab Audit...\n');
    
    await this.init();
    
    try {
      await this.testPageLoad();
      await this.testNavigation();
      await this.testResourceTypeTabs();
      await this.testSearchAndFilters();
      await this.testResourceCards();
      await this.testResourceDetailPages();
      
    } catch (error) {
      console.error('Audit error:', error);
    } finally {
      await this.cleanup();
    }

    // Print summary
    console.log('\n📊 AUDIT SUMMARY');
    console.log('================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`📊 Success Rate: ${Math.round((passed / this.results.length) * 100)}%`);
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.filter(r => r.status === 'FAIL').forEach(r => {
        console.log(`   • ${r.test}: ${r.details}`);
      });
    }
    
    return this.results;
  }
}

// Run the audit
if (require.main === module) {
  const audit = new ResourcesAudit();
  audit.run().then(() => {
    console.log('\n🎯 Audit completed!');
    process.exit(0);
  }).catch(error => {
    console.error('Audit failed:', error);
    process.exit(1);
  });
}

export default ResourcesAudit;
