#!/bin/bash

BASE_URL="http://localhost:3000/api"
SESSION_ID=""

echo "🌐 Testing Interview Practice HTTP API Endpoints with cURL..."
echo

# Test 1: GET /api/interview-practice (List Sessions)
echo "1. Testing GET /api/interview-practice..."
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/interview-practice")
http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 200 ]; then
    echo "   ✅ Status: $http_code"
    echo "   ✅ Response: $(echo $body | jq -r '.success // "No success field"')"
else
    echo "   ❌ Status: $http_code"
    echo "   ❌ Error: $body"
fi

# Test 2: POST /api/interview-practice (Create Session)
echo
echo "2. Testing POST /api/interview-practice..."
create_payload='{
  "sessionType": "QUICK_PRACTICE",
  "experienceLevel": "INTERMEDIATE",
  "difficulty": "INTERMEDIATE",
  "totalQuestions": 3,
  "careerPath": "Software Engineering",
  "companyType": "Tech Startup",
  "industryFocus": "Technology",
  "specificRole": "Full Stack Developer",
  "interviewType": "VIDEO",
  "preparationTime": "15 minutes",
  "focusAreas": ["Problem Solving", "Technical Skills"]
}'

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/interview-practice" \
  -H "Content-Type: application/json" \
  -d "$create_payload")

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 200 ] || [ $http_code -eq 201 ]; then
    echo "   ✅ Status: $http_code"
    SESSION_ID=$(echo $body | jq -r '.data.id // empty')
    if [ -n "$SESSION_ID" ]; then
        echo "   ✅ Session created: $SESSION_ID"
        echo "   ✅ Session type: $(echo $body | jq -r '.data.sessionType // "N/A"')"
    else
        echo "   ⚠️  Session ID not found in response"
    fi
else
    echo "   ❌ Status: $http_code"
    echo "   ❌ Error: $body"
    exit 1
fi

# Test 3: GET /api/interview-practice/[sessionId] (Get Session Details)
if [ -n "$SESSION_ID" ]; then
    echo
    echo "3. Testing GET /api/interview-practice/[sessionId]..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/interview-practice/$SESSION_ID")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ]; then
        echo "   ✅ Status: $http_code"
        echo "   ✅ Session retrieved: $(echo $body | jq -r '.data.id // "N/A"')"
        echo "   ✅ Questions count: $(echo $body | jq -r '.data.questions | length // 0')"
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

# Test 4: POST /api/interview-practice/[sessionId]/questions (Generate Questions)
if [ -n "$SESSION_ID" ]; then
    echo
    echo "4. Testing POST /api/interview-practice/[sessionId]/questions..."
    questions_payload='{
      "count": 3,
      "difficulty": "INTERMEDIATE",
      "focusAreas": ["Problem Solving", "Technical Skills"]
    }'

    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/interview-practice/$SESSION_ID/questions" \
      -H "Content-Type: application/json" \
      -d "$questions_payload")

    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ] || [ $http_code -eq 201 ]; then
        echo "   ✅ Status: $http_code"
        echo "   ✅ Questions generated: $(echo $body | jq -r '.data.questions | length // 0')"
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

# Test 5: GET /api/interview-practice/[sessionId]/questions (Get Questions)
if [ -n "$SESSION_ID" ]; then
    echo
    echo "5. Testing GET /api/interview-practice/[sessionId]/questions..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/interview-practice/$SESSION_ID/questions")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ]; then
        echo "   ✅ Status: $http_code"
        questions_count=$(echo $body | jq -r '.data | length // 0')
        echo "   ✅ Questions retrieved: $questions_count"
        if [ $questions_count -gt 0 ]; then
            first_question=$(echo $body | jq -r '.data[0].questionText // "N/A"' | cut -c1-50)
            echo "   ✅ First question: \"$first_question...\""
            FIRST_QUESTION_ID=$(echo $body | jq -r '.data[0].id // empty')
        fi
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

# Test 6: POST /api/interview-practice/[sessionId]/responses (Submit Response)
if [ -n "$SESSION_ID" ] && [ -n "$FIRST_QUESTION_ID" ]; then
    echo
    echo "6. Testing POST /api/interview-practice/[sessionId]/responses..."
    response_payload="{
      \"questionId\": \"$FIRST_QUESTION_ID\",
      \"responseText\": \"I am a passionate software developer with experience in full-stack development. I have worked on various projects using modern technologies like React, Node.js, and PostgreSQL. I enjoy solving complex problems and continuously learning new technologies to improve my skills.\",
      \"responseTime\": 120,
      \"preparationTime\": 30,
      \"userNotes\": \"Felt confident about this response\"
    }"

    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/interview-practice/$SESSION_ID/responses" \
      -H "Content-Type: application/json" \
      -d "$response_payload")

    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ] || [ $http_code -eq 201 ]; then
        echo "   ✅ Status: $http_code"
        echo "   ✅ Response submitted: $(echo $body | jq -r '.data.id // "N/A"')"
        echo "   ✅ Response completed: $(echo $body | jq -r '.data.isCompleted // false')"
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

# Test 7: GET /api/interview-practice/[sessionId]/responses (Get Responses)
if [ -n "$SESSION_ID" ]; then
    echo
    echo "7. Testing GET /api/interview-practice/[sessionId]/responses..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/interview-practice/$SESSION_ID/responses")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ]; then
        echo "   ✅ Status: $http_code"
        echo "   ✅ Responses retrieved: $(echo $body | jq -r '.data | length // 0')"
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

# Test 8: PATCH /api/interview-practice/[sessionId] (Update Session)
if [ -n "$SESSION_ID" ]; then
    echo
    echo "8. Testing PATCH /api/interview-practice/[sessionId]..."
    update_payload='{
      "status": "COMPLETED",
      "timeSpent": 15,
      "overallScore": 8.5
    }'

    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X PATCH "$BASE_URL/interview-practice/$SESSION_ID" \
      -H "Content-Type: application/json" \
      -d "$update_payload")

    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ]; then
        echo "   ✅ Status: $http_code"
        echo "   ✅ Session updated: $(echo $body | jq -r '.data.status // "N/A"')"
        echo "   ✅ Overall score: $(echo $body | jq -r '.data.overallScore // "N/A"')"
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

# Test 9: GET /api/interview-practice/progress (Get Progress)
echo
echo "9. Testing GET /api/interview-practice/progress..."
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/interview-practice/progress")
http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 200 ]; then
    echo "   ✅ Status: $http_code"
    echo "   ✅ Progress data retrieved"
    echo "   ✅ Total sessions: $(echo $body | jq -r '.data.overallProgress.totalSessions // 0')"
    echo "   ✅ Average score: $(echo $body | jq -r '.data.overallProgress.averageScore // "N/A"')"
else
    echo "   ❌ Status: $http_code"
    echo "   ❌ Error: $body"
fi

# Test 10: DELETE /api/interview-practice/[sessionId] (Delete Session)
if [ -n "$SESSION_ID" ]; then
    echo
    echo "10. Testing DELETE /api/interview-practice/[sessionId]..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X DELETE "$BASE_URL/interview-practice/$SESSION_ID")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ]; then
        echo "   ✅ Status: $http_code"
        echo "   ✅ Session deleted successfully"
    else
        echo "   ❌ Status: $http_code"
        echo "   ❌ Error: $body"
    fi
fi

echo
echo "🎉 All Interview Practice HTTP API endpoints tested!"
