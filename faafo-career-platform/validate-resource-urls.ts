#!/usr/bin/env npx tsx

/**
 * Validate Resource URLs
 * Tests external URLs from static resources to ensure they're valid and accessible
 */

import { getAllStaticResources } from './src/lib/staticResources';

interface URLTestResult {
  url: string;
  title: string;
  status: number | 'ERROR';
  accessible: boolean;
  error?: string;
}

async function testURL(url: string, title: string): Promise<URLTestResult> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ResourceValidator/1.0)'
      }
    });

    clearTimeout(timeoutId);

    return {
      url,
      title,
      status: response.status,
      accessible: response.ok
    };
  } catch (error) {
    return {
      url,
      title,
      status: 'ERROR',
      accessible: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function validateResourceURLs() {
  console.log('🔍 Validating Resource URLs...\n');

  const staticResources = getAllStaticResources();
  const results: URLTestResult[] = [];

  // Test first 10 URLs to avoid overwhelming servers
  const urlsToTest = staticResources.slice(0, 10);

  console.log(`Testing ${urlsToTest.length} URLs...\n`);

  for (const resource of urlsToTest) {
    console.log(`Testing: ${resource.title}`);
    const result = await testURL(resource.url, resource.title);
    results.push(result);

    const emoji = result.accessible ? '✅' : '❌';
    const status = result.status === 'ERROR' ? 'ERROR' : result.status;
    console.log(`${emoji} ${status} - ${result.title}`);
    
    if (!result.accessible && result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    // Small delay to be respectful to servers
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Summary
  console.log('\n📊 VALIDATION SUMMARY');
  console.log('====================');
  
  const accessible = results.filter(r => r.accessible).length;
  const total = results.length;
  const successRate = Math.round((accessible / total) * 100);
  
  console.log(`✅ Accessible: ${accessible}/${total} (${successRate}%)`);
  console.log(`❌ Failed: ${total - accessible}/${total}`);
  
  if (accessible < total) {
    console.log('\n❌ FAILED URLs:');
    results.filter(r => !r.accessible).forEach(r => {
      console.log(`   • ${r.title}: ${r.url}`);
      if (r.error) {
        console.log(`     Error: ${r.error}`);
      }
    });
  }

  return results;
}

// Run validation
if (require.main === module) {
  validateResourceURLs().then(() => {
    console.log('\n🎯 URL validation completed!');
    process.exit(0);
  }).catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

export default validateResourceURLs;
