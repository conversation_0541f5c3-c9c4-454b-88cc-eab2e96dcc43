const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword'
};

let authCookie = '';
let testSessionId = '';

async function makeRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Cookie': authCookie,
      ...options.headers
    }
  };

  const response = await fetch(url, { ...defaultOptions, ...options });
  const data = await response.json();
  
  return { response, data };
}

async function testInterviewHTTPAPI() {
  try {
    console.log('🌐 Testing Interview Practice HTTP API Endpoints...\n');

    // Step 1: Login to get authentication
    console.log('1. Authenticating user...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/signin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password
      })
    });

    if (loginResponse.headers.get('set-cookie')) {
      authCookie = loginResponse.headers.get('set-cookie');
      console.log('   ✅ Authentication successful');
    } else {
      console.log('   ⚠️  Authentication may not be required for API testing');
    }

    // Step 2: Test GET /api/interview-practice (List Sessions)
    console.log('\n2. Testing GET /api/interview-practice...');
    const { response: listResponse, data: listData } = await makeRequest('/interview-practice');
    
    if (listResponse.ok) {
      console.log(`   ✅ Status: ${listResponse.status}`);
      console.log(`   ✅ Sessions found: ${listData.data?.sessions?.length || 0}`);
    } else {
      console.log(`   ❌ Status: ${listResponse.status}`);
      console.log(`   ❌ Error: ${listData.error}`);
    }

    // Step 3: Test POST /api/interview-practice (Create Session)
    console.log('\n3. Testing POST /api/interview-practice...');
    const sessionConfig = {
      sessionType: 'QUICK_PRACTICE',
      experienceLevel: 'INTERMEDIATE',
      difficulty: 'INTERMEDIATE',
      totalQuestions: 3,
      careerPath: 'Software Engineering',
      companyType: 'Tech Startup',
      industryFocus: 'Technology',
      specificRole: 'Full Stack Developer',
      interviewType: 'VIDEO',
      preparationTime: '15 minutes',
      focusAreas: ['Problem Solving', 'Technical Skills']
    };

    const { response: createResponse, data: createData } = await makeRequest('/interview-practice', {
      method: 'POST',
      body: JSON.stringify(sessionConfig)
    });

    if (createResponse.ok && createData.success) {
      testSessionId = createData.data.id;
      console.log(`   ✅ Status: ${createResponse.status}`);
      console.log(`   ✅ Session created: ${testSessionId}`);
      console.log(`   ✅ Session type: ${createData.data.sessionType}`);
    } else {
      console.log(`   ❌ Status: ${createResponse.status}`);
      console.log(`   ❌ Error: ${createData.error}`);
      return;
    }

    // Step 4: Test GET /api/interview-practice/[sessionId] (Get Session Details)
    console.log('\n4. Testing GET /api/interview-practice/[sessionId]...');
    const { response: detailResponse, data: detailData } = await makeRequest(`/interview-practice/${testSessionId}`);
    
    if (detailResponse.ok && detailData.success) {
      console.log(`   ✅ Status: ${detailResponse.status}`);
      console.log(`   ✅ Session retrieved: ${detailData.data.id}`);
      console.log(`   ✅ Questions count: ${detailData.data.questions?.length || 0}`);
    } else {
      console.log(`   ❌ Status: ${detailResponse.status}`);
      console.log(`   ❌ Error: ${detailData.error}`);
    }

    // Step 5: Test POST /api/interview-practice/[sessionId]/questions (Generate Questions)
    console.log('\n5. Testing POST /api/interview-practice/[sessionId]/questions...');
    const questionConfig = {
      count: 3,
      difficulty: 'INTERMEDIATE',
      focusAreas: ['Problem Solving', 'Technical Skills']
    };

    const { response: questionsResponse, data: questionsData } = await makeRequest(`/interview-practice/${testSessionId}/questions`, {
      method: 'POST',
      body: JSON.stringify(questionConfig)
    });

    if (questionsResponse.ok && questionsData.success) {
      console.log(`   ✅ Status: ${questionsResponse.status}`);
      console.log(`   ✅ Questions generated: ${questionsData.data?.questions?.length || 0}`);
    } else {
      console.log(`   ❌ Status: ${questionsResponse.status}`);
      console.log(`   ❌ Error: ${questionsData.error}`);
    }

    // Step 6: Test GET /api/interview-practice/[sessionId]/questions (Get Questions)
    console.log('\n6. Testing GET /api/interview-practice/[sessionId]/questions...');
    const { response: getQuestionsResponse, data: getQuestionsData } = await makeRequest(`/interview-practice/${testSessionId}/questions`);
    
    let firstQuestionId = '';
    if (getQuestionsResponse.ok && getQuestionsData.success) {
      const questions = getQuestionsData.data;
      firstQuestionId = questions[0]?.id;
      console.log(`   ✅ Status: ${getQuestionsResponse.status}`);
      console.log(`   ✅ Questions retrieved: ${questions.length}`);
      console.log(`   ✅ First question: "${questions[0]?.questionText?.substring(0, 50)}..."`);
    } else {
      console.log(`   ❌ Status: ${getQuestionsResponse.status}`);
      console.log(`   ❌ Error: ${getQuestionsData.error}`);
    }

    // Step 7: Test POST /api/interview-practice/[sessionId]/responses (Submit Response)
    if (firstQuestionId) {
      console.log('\n7. Testing POST /api/interview-practice/[sessionId]/responses...');
      const responseData = {
        questionId: firstQuestionId,
        responseText: "I am a passionate software developer with experience in full-stack development. I have worked on various projects using modern technologies like React, Node.js, and PostgreSQL. I enjoy solving complex problems and continuously learning new technologies to improve my skills.",
        responseTime: 120,
        preparationTime: 30,
        userNotes: "Felt confident about this response"
      };

      const { response: submitResponse, data: submitData } = await makeRequest(`/interview-practice/${testSessionId}/responses`, {
        method: 'POST',
        body: JSON.stringify(responseData)
      });

      if (submitResponse.ok && submitData.success) {
        console.log(`   ✅ Status: ${submitResponse.status}`);
        console.log(`   ✅ Response submitted: ${submitData.data.id}`);
        console.log(`   ✅ Response completed: ${submitData.data.isCompleted}`);
      } else {
        console.log(`   ❌ Status: ${submitResponse.status}`);
        console.log(`   ❌ Error: ${submitData.error}`);
      }
    }

    // Step 8: Test GET /api/interview-practice/[sessionId]/responses (Get Responses)
    console.log('\n8. Testing GET /api/interview-practice/[sessionId]/responses...');
    const { response: getResponsesResponse, data: getResponsesData } = await makeRequest(`/interview-practice/${testSessionId}/responses`);
    
    if (getResponsesResponse.ok && getResponsesData.success) {
      console.log(`   ✅ Status: ${getResponsesResponse.status}`);
      console.log(`   ✅ Responses retrieved: ${getResponsesData.data.length}`);
    } else {
      console.log(`   ❌ Status: ${getResponsesResponse.status}`);
      console.log(`   ❌ Error: ${getResponsesData.error}`);
    }

    // Step 9: Test PATCH /api/interview-practice/[sessionId] (Update Session)
    console.log('\n9. Testing PATCH /api/interview-practice/[sessionId]...');
    const updatePayload = {
      status: 'COMPLETED',
      timeSpent: 15,
      overallScore: 8.5
    };

    const { response: updateResponse, data: updateData } = await makeRequest(`/interview-practice/${testSessionId}`, {
      method: 'PATCH',
      body: JSON.stringify(updatePayload)
    });

    if (updateResponse.ok && updateData.success) {
      console.log(`   ✅ Status: ${updateResponse.status}`);
      console.log(`   ✅ Session updated: ${updateData.data.status}`);
      console.log(`   ✅ Overall score: ${updateData.data.overallScore}`);
    } else {
      console.log(`   ❌ Status: ${updateResponse.status}`);
      console.log(`   ❌ Error: ${updateData.error}`);
    }

    // Step 10: Test GET /api/interview-practice/progress (Get Progress)
    console.log('\n10. Testing GET /api/interview-practice/progress...');
    const { response: progressResponse, data: progressData } = await makeRequest('/interview-practice/progress');
    
    if (progressResponse.ok && progressData.success) {
      console.log(`   ✅ Status: ${progressResponse.status}`);
      console.log(`   ✅ Progress data retrieved`);
      console.log(`   ✅ Total sessions: ${progressData.data.overallProgress?.totalSessions || 0}`);
      console.log(`   ✅ Average score: ${progressData.data.overallProgress?.averageScore || 'N/A'}`);
    } else {
      console.log(`   ❌ Status: ${progressResponse.status}`);
      console.log(`   ❌ Error: ${progressData.error}`);
    }

    // Step 11: Test DELETE /api/interview-practice/[sessionId] (Delete Session)
    console.log('\n11. Testing DELETE /api/interview-practice/[sessionId]...');
    const { response: deleteResponse, data: deleteData } = await makeRequest(`/interview-practice/${testSessionId}`, {
      method: 'DELETE'
    });

    if (deleteResponse.ok && deleteData.success) {
      console.log(`   ✅ Status: ${deleteResponse.status}`);
      console.log(`   ✅ Session deleted successfully`);
    } else {
      console.log(`   ❌ Status: ${deleteResponse.status}`);
      console.log(`   ❌ Error: ${deleteData.error}`);
    }

    console.log('\n🎉 All Interview Practice HTTP API endpoints tested successfully!');
    
  } catch (error) {
    console.error('❌ HTTP API testing failed:', error);
    process.exit(1);
  }
}

// Add delay to ensure server is ready
setTimeout(() => {
  testInterviewHTTPAPI();
}, 2000);
